/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
AuraBloomAudioProcessorEditor::AuraBloomAudioProcessorEditor (AuraBloomAudioProcessor& p)
    : AudioProcessorEditor (&p), audioProcessor (p), bloomChamber(p.getParticleSystem(), p.getGrainGenerator())
{
    // First step test: Add basic UI components, but don't bind parameters
    setLookAndFeel(&lookAndFeel);

    // Add BloomChamber visualizer
    addAndMakeVisible(bloomChamber);

    // Add macro control panel
    addAndMakeVisible(macroControlPanel);

    // Setup BloomChamber XY control callback
    bloomChamber.onXYParameterChange = [this](const juce::String& parameterId, float value)
    {
        // Connect to actual parameter control here
        DBG("XY Parameter Change: " + parameterId + " = " + juce::String(value));

        // Future: connect to AudioProcessor parameters
        // audioProcessor.setParameterNotifyingHost(parameterId, value);
    };

    // Setup macro control panel parameter change callback
    macroControlPanel.setParameterChangeCallback([this](const juce::String& parameterId, float value)
    {
        DBG("Macro Parameter Change: " + parameterId + " = " + juce::String(value));

        // Future: connect to AudioProcessor parameters
        // audioProcessor.setParameterNotifyingHost(parameterId, value);
    });

    // 添加一个简单的滑块（不绑定参数）
    mixSlider.setSliderStyle(juce::Slider::LinearHorizontal);
    mixSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 80, 20);
    mixSlider.setRange(0.0, 1.0, 0.01);
    mixSlider.setValue(0.5);
    addAndMakeVisible(mixSlider);

    // 添加标签
    mixLabel.setText("Mix", juce::dontSendNotification);
    mixLabel.setJustificationType(juce::Justification::centred);
    mixLabel.setColour(juce::Label::textColourId, juce::Colours::white);
    addAndMakeVisible(mixLabel);

    // 第二步测试：添加参数绑定
    try {
        mixAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            audioProcessor.getParameters(), AuraBloomParameters::MIX_ID, mixSlider);
        DBG("Mix parameter attachment created successfully");
    } catch (const std::exception& e) {
        DBG("Exception creating mix attachment: " + juce::String(e.what()));
    } catch (...) {
        DBG("Unknown exception creating mix attachment");
    }

    // 第三步测试：添加Tab面板
    try {
        // 创建一个简单的Tab面板
        addAndMakeVisible(tabs);

        // 创建一个简单的面板作为Tab内容
        emittersPanel = std::make_unique<ResponsivePanel>();
        emittersPanel->setName("Emitters");
        emittersPanel->setColour(juce::ResizableWindow::backgroundColourId, juce::Colour(40, 40, 80));

        // 完善Emitters面板：添加多个控件
        try {
            // Rate滑块
            emitterRateSlider.setSliderStyle(juce::Slider::LinearHorizontal);
            emitterRateSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 80, 20);
            emitterRateSlider.setRange(0.1, 100.0, 0.1);
            emitterRateSlider.setValue(10.0);
            emittersPanel->addAndMakeVisible(emitterRateSlider);

            emitterRateLabel.setText("Rate", juce::dontSendNotification);
            emitterRateLabel.setJustificationType(juce::Justification::centred);
            emitterRateLabel.setColour(juce::Label::textColourId, juce::Colours::white);
            emittersPanel->addAndMakeVisible(emitterRateLabel);

            // Shape ComboBox
            emitterShapeCombo.addItem("Point", 1);
            emitterShapeCombo.addItem("Line", 2);
            emitterShapeCombo.addItem("Circle", 3);
            emitterShapeCombo.addItem("Square", 4);
            emitterShapeCombo.setSelectedId(1);
            emittersPanel->addAndMakeVisible(emitterShapeCombo);

            emitterShapeLabel.setText("Shape", juce::dontSendNotification);
            emitterShapeLabel.setJustificationType(juce::Justification::centred);
            emitterShapeLabel.setColour(juce::Label::textColourId, juce::Colours::white);
            emittersPanel->addAndMakeVisible(emitterShapeLabel);

            // Velocity滑块
            emitterVelocitySlider.setSliderStyle(juce::Slider::LinearHorizontal);
            emitterVelocitySlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 80, 20);
            emitterVelocitySlider.setRange(0.0, 100.0, 0.1);
            emitterVelocitySlider.setValue(50.0);
            emittersPanel->addAndMakeVisible(emitterVelocitySlider);

            emitterVelocityLabel.setText("Velocity", juce::dontSendNotification);
            emitterVelocityLabel.setJustificationType(juce::Justification::centred);
            emitterVelocityLabel.setColour(juce::Label::textColourId, juce::Colours::white);
            emittersPanel->addAndMakeVisible(emitterVelocityLabel);

            // Spread滑块
            emitterSpreadSlider.setSliderStyle(juce::Slider::LinearHorizontal);
            emitterSpreadSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 80, 20);
            emitterSpreadSlider.setRange(0.0, 360.0, 1.0);
            emitterSpreadSlider.setValue(45.0);
            emittersPanel->addAndMakeVisible(emitterSpreadSlider);

            emitterSpreadLabel.setText("Spread", juce::dontSendNotification);
            emitterSpreadLabel.setJustificationType(juce::Justification::centred);
            emitterSpreadLabel.setColour(juce::Label::textColourId, juce::Colours::white);
            emittersPanel->addAndMakeVisible(emitterSpreadLabel);

            // Burst切换按钮
            emitterBurstToggle.setButtonText("Burst Mode");
            emitterBurstToggle.setColour(juce::ToggleButton::textColourId, juce::Colours::white);
            emittersPanel->addAndMakeVisible(emitterBurstToggle);

            // 设置Emitters面板的布局函数
            emittersPanel->layoutFunction = [this]() {
                auto area = emittersPanel->getLocalBounds();
                area.reduce(10, 10);

                int rowHeight = 50;
                int labelHeight = 20;
                int controlHeight = 30;

                // Rate控件
                auto rateArea = area.removeFromTop(rowHeight);
                emitterRateLabel.setBounds(rateArea.removeFromTop(labelHeight));
                emitterRateSlider.setBounds(rateArea.removeFromTop(controlHeight));

                area.removeFromTop(5); // Spacing

                // Shape控件
                auto shapeArea = area.removeFromTop(rowHeight);
                emitterShapeLabel.setBounds(shapeArea.removeFromTop(labelHeight));
                emitterShapeCombo.setBounds(shapeArea.removeFromTop(controlHeight));

                area.removeFromTop(5); // Spacing

                // Velocity controls
                auto velocityArea = area.removeFromTop(rowHeight);
                emitterVelocityLabel.setBounds(velocityArea.removeFromTop(labelHeight));
                emitterVelocitySlider.setBounds(velocityArea.removeFromTop(controlHeight));

                area.removeFromTop(5); // Spacing

                // Spread controls
                auto spreadArea = area.removeFromTop(rowHeight);
                emitterSpreadLabel.setBounds(spreadArea.removeFromTop(labelHeight));
                emitterSpreadSlider.setBounds(spreadArea.removeFromTop(controlHeight));

                area.removeFromTop(5); // Spacing

                // Burst切换按钮
                emitterBurstToggle.setBounds(area.removeFromTop(30));
            };

            DBG("Emitters panel controls added successfully");
        } catch (const std::exception& e) {
            DBG("Exception adding controls to emitters panel: " + juce::String(e.what()));
        } catch (...) {
            DBG("Unknown exception adding controls to emitters panel");
        }

        // 添加Tab
        tabs.addTab("Emitters", juce::Colour(40, 40, 80), emittersPanel.get(), false);

        DBG("Tab panel created successfully");
    } catch (const std::exception& e) {
        DBG("Exception creating tab panel: " + juce::String(e.what()));
    } catch (...) {
        DBG("Unknown exception creating tab panel");
    }

    setSize(1000, 700); // Increased height for macro control panel

    // TEMPORARILY DISABLED: Rate parameter binding causes slider lock
    // TODO: Investigate why parameter binding prevents slider movement
    DBG("Rate parameter binding temporarily disabled - slider should be movable");
}

AuraBloomAudioProcessorEditor::~AuraBloomAudioProcessorEditor()
{
    setLookAndFeel(nullptr);
}

//==============================================================================
void AuraBloomAudioProcessorEditor::paint (juce::Graphics& g)
{
    // Simple background drawing
    g.fillAll(juce::Colour(40, 40, 80));
    g.setColour(juce::Colours::white);
    g.setFont(20.0f);
    g.drawText("AuraBloom - Ultra Anti-Click", 10, 10, 500, 30, juce::Justification::left, true);
}

void AuraBloomAudioProcessorEditor::resized()
{
    // New layout: macro controls on top, left-right split below
    auto area = getLocalBounds();
    area.removeFromTop(50); // Space for title

    // Top: macro control panel (160px height for better spacing)
    auto macroArea = area.removeFromTop(160);
    macroArea.reduce(10, 5);
    macroControlPanel.setBounds(macroArea);

    area.removeFromTop(10); // Spacing

    // Bottom: left BloomChamber, right control panel
    // Left: BloomChamber with integrated XY control (65% width)
    auto leftArea = area.removeFromLeft(static_cast<int>(area.getWidth() * 0.65f));
    leftArea.reduce(10, 10);
    bloomChamber.setBounds(leftArea);

    // Right: control panel (35% width)
    auto rightArea = area;
    rightArea.reduce(10, 10);

    // Layout Mix controls
    auto mixArea = rightArea.removeFromTop(60);
    mixLabel.setBounds(mixArea.removeFromTop(20));
    mixSlider.setBounds(mixArea);

    // Layout Tab panel
    rightArea.removeFromTop(10); // Spacing
    tabs.setBounds(rightArea);
}

void AuraBloomAudioProcessorEditor::setupKnob(juce::Slider& knob, juce::Label& label, const juce::String& labelText)
{
    // 空实现
}

void AuraBloomAudioProcessorEditor::setupSlider(juce::Slider& slider, juce::Label& label, const juce::String& labelText)
{
    // 空实现
}

void AuraBloomAudioProcessorEditor::createTabPanels()
{
    // 空实现
}

void AuraBloomAudioProcessorEditor::setupEmittersPanel()
{
    // 空实现
}

void AuraBloomAudioProcessorEditor::setupParticlesPanel()
{
    // 空实现
}

void AuraBloomAudioProcessorEditor::setupModulationPanel()
{
    // 空实现
}

void AuraBloomAudioProcessorEditor::setupEffectsPanel()
{
    // 空实现
}
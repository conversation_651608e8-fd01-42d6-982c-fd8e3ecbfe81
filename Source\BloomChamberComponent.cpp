/*
  ==============================================================================

    BloomChamberComponent.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    Enhanced implementation with real-time particle visualization and interaction.

  ==============================================================================
*/

#include "BloomChamberComponent.h"

BloomChamberComponent::BloomChamberComponent(ParticleSystem& ps, GrainGenerator& gg)
    : particleSystem(ps), grainGenerator(gg)
{
    // Enable mouse interaction
    setInterceptsMouseClicks(true, true);

    // Start animation timer at 30 FPS for smooth visuals
    startTimer(33);
}

BloomChamberComponent::~BloomChamberComponent()
{
    stopTimer();
}

void BloomChamberComponent::paint(juce::Graphics& g)
{
    // Draw in layers for best visual effect
    drawBackground(g);

    // Draw XY grid if enabled
    if (xyControlEnabled && showXYGrid)
        drawXYGrid(g);

    drawParticles(g);
    drawInteractionEffects(g);

    // Draw XY control elements
    if (xyControlEnabled)
    {
        drawXYTrail(g);
        drawXYControlPoint(g);
    }

    drawInfoOverlay(g);
}

void BloomChamberComponent::drawBackground(juce::Graphics& g)
{
    // Create a beautiful gradient background
    auto bounds = getLocalBounds().toFloat();

    juce::ColourGradient gradient(
        juce::Colour(15, 15, 25),    // Dark blue-black
        bounds.getCentreX(), bounds.getY(),
        juce::Colour(25, 15, 35),    // Deep purple
        bounds.getCentreX(), bounds.getBottom(),
        false
    );

    // Add some color stops for more depth
    gradient.addColour(0.3, juce::Colour(20, 20, 40));
    gradient.addColour(0.7, juce::Colour(30, 20, 45));

    g.setGradientFill(gradient);
    g.fillAll();

    // Add subtle grid pattern
    g.setColour(juce::Colours::white.withAlpha(0.05f));
    float gridSize = 20.0f;

    for (float x = 0; x < bounds.getWidth(); x += gridSize)
    {
        g.drawVerticalLine(static_cast<int>(x), 0.0f, bounds.getHeight());
    }

    for (float y = 0; y < bounds.getHeight(); y += gridSize)
    {
        g.drawHorizontalLine(static_cast<int>(y), 0.0f, bounds.getWidth());
    }
}

void BloomChamberComponent::drawParticles(juce::Graphics& g)
{
    // Get active grain count for visualization
    int activeGrains = grainGenerator.getActiveGrainCount();

    if (activeGrains == 0)
    {
        // Show "waiting for audio" message
        g.setColour(juce::Colours::white.withAlpha(0.3f));
        g.setFont(16.0f);
        g.drawText("等待音频输入...", getLocalBounds(), juce::Justification::centred);
        return;
    }

    auto bounds = getLocalBounds().toFloat();

    // Simulate particle positions based on grain generator state
    // Since we don't have direct access to individual grains, we'll create
    // a visual representation based on the system state

    for (int i = 0; i < juce::jmin(activeGrains, 200); ++i) // Limit for performance
    {
        // Create pseudo-random but stable positions based on grain index and time
        float normalizedIndex = static_cast<float>(i) / static_cast<float>(activeGrains);
        float timeOffset = animationTime * 0.1f;

        // Generate particle position with some movement
        float x = bounds.getWidth() * (0.2f + 0.6f * std::sin(normalizedIndex * 6.28f + timeOffset));
        float y = bounds.getHeight() * (0.2f + 0.6f * std::cos(normalizedIndex * 4.0f + timeOffset * 0.7f));

        // Add some randomness
        x += 20.0f * std::sin(normalizedIndex * 23.0f + timeOffset * 2.0f);
        y += 20.0f * std::cos(normalizedIndex * 17.0f + timeOffset * 1.5f);

        // Calculate particle properties
        float age = std::fmod(normalizedIndex + timeOffset, 1.0f);
        float velocity = 0.5f + 0.5f * std::sin(normalizedIndex * 10.0f + timeOffset);
        float pitch = 0.5f + 0.5f * std::cos(normalizedIndex * 8.0f);

        // Get color and alpha
        auto color = getParticleColor(age, velocity, pitch);
        float alpha = getParticleAlpha(age, 1.0f);

        // Draw particle based on visualization mode
        g.setColour(color.withAlpha(alpha));

        switch (visualizationMode)
        {
            case 0: // Dots
            {
                float size = particleSize * (0.5f + 0.5f * velocity);
                g.fillEllipse(x - size/2, y - size/2, size, size);
                break;
            }
            case 1: // Lines (trails)
            {
                float prevX = x - velocity * 10.0f * std::cos(normalizedIndex * 5.0f);
                float prevY = y - velocity * 10.0f * std::sin(normalizedIndex * 5.0f);
                g.drawLine(prevX, prevY, x, y, particleSize * 0.5f);
                break;
            }
            case 2: // Glow
            {
                float glowSize = particleSize * 2.0f * (0.3f + 0.7f * velocity);

                // Draw multiple circles for glow effect
                for (int glow = 3; glow >= 1; --glow)
                {
                    float glowAlpha = alpha * 0.3f / glow;
                    g.setColour(color.withAlpha(glowAlpha));
                    float currentSize = glowSize * glow;
                    g.fillEllipse(x - currentSize/2, y - currentSize/2, currentSize, currentSize);
                }
                break;
            }
        }
    }
}

void BloomChamberComponent::drawInteractionEffects(juce::Graphics& g)
{
    if (isDragging)
    {
        // Draw interaction radius
        g.setColour(isAttractorMode ?
                   juce::Colours::green.withAlpha(0.3f) :  // Green for attractor
                   juce::Colours::red.withAlpha(0.3f));    // Red for repulsor

        g.drawEllipse(mousePosition.x - interactionRadius,
                     mousePosition.y - interactionRadius,
                     interactionRadius * 2,
                     interactionRadius * 2, 2.0f);

        // Draw center point
        g.setColour(isAttractorMode ?
                   juce::Colours::lightgreen.withAlpha(0.8f) :
                   juce::Colours::lightcoral.withAlpha(0.8f));

        g.fillEllipse(mousePosition.x - 3, mousePosition.y - 3, 6, 6);

        // Draw force lines
        g.setColour(juce::Colours::white.withAlpha(0.2f));
        for (int i = 0; i < 8; ++i)
        {
            float angle = i * juce::MathConstants<float>::twoPi / 8.0f;
            float startRadius = 8.0f;
            float endRadius = interactionRadius * 0.8f;

            float startX = mousePosition.x + startRadius * std::cos(angle);
            float startY = mousePosition.y + startRadius * std::sin(angle);
            float endX = mousePosition.x + endRadius * std::cos(angle);
            float endY = mousePosition.y + endRadius * std::sin(angle);

            if (isAttractorMode)
            {
                // Arrows pointing inward for attractor
                g.drawLine(endX, endY, startX, startY, 1.0f);
            }
            else
            {
                // Arrows pointing outward for repulsor
                g.drawLine(startX, startY, endX, endY, 1.0f);
            }
        }
    }
}

void BloomChamberComponent::drawInfoOverlay(juce::Graphics& g)
{
    // Draw system info in top-left corner
    g.setColour(juce::Colours::white.withAlpha(0.6f));
    g.setFont(12.0f);

    juce::String info;
    info << "Active Grains: " << grainGenerator.getActiveGrainCount() << "\n";

    if (xyControlEnabled)
    {
        info << "XY: " << juce::String(xyPosition.x, 2) << ", " << juce::String(xyPosition.y, 2) << "\n";
        info << "Mode: " << (isXYControlActive ? "XY Control" : "Particle") << "\n";
    }
    else
    {
        info << "Mode: " << (isAttractorMode ? "Attract" : "Repel") << "\n";
    }

    info << "Visual: " << (visualizationMode == 0 ? "Points" :
                        visualizationMode == 1 ? "Trails" : "Glow");

    g.drawText(info, 10, 10, 150, 80, juce::Justification::topLeft);

    // Draw interaction hint in bottom-right
    if (!isDragging)
    {
        g.setColour(juce::Colours::white.withAlpha(0.4f));
        g.setFont(11.0f);

        juce::String hint;
        if (xyControlEnabled)
        {
            hint = "Drag: XY Control\nCtrl+Drag: Particle Mode\nShift+Drag: Toggle Mode";
        }
        else
        {
            hint = "Drag: Interact\nShift+Drag: Toggle Mode";
        }

        g.drawText(hint, getWidth() - 160, getHeight() - 60, 150, 50,
                  juce::Justification::bottomRight);
    }
}

void BloomChamberComponent::resized()
{
    // Update interaction radius based on component size
    auto bounds = getLocalBounds();
    interactionRadius = juce::jmin(bounds.getWidth(), bounds.getHeight()) * 0.1f;
    interactionRadius = juce::jlimit(20.0f, 100.0f, interactionRadius);
}

void BloomChamberComponent::mouseDown(const juce::MouseEvent& e)
{
    isDragging = true;
    mousePosition = e.position;
    dragStartPosition = e.position;

    // XY Control mode (default) vs Particle interaction mode (with Ctrl)
    if (xyControlEnabled && !e.mods.isCtrlDown())
    {
        isXYControlActive = true;
        xyPosition = screenToNormalizedXY(e.position);
        xyTrail.clear();
        xyTrail.add(xyPosition);
        updateXYParameters();
    }
    else
    {
        isXYControlActive = false;
        // Check if shift is held to toggle attractor/repulsor mode
        if (e.mods.isShiftDown())
        {
            isAttractorMode = !isAttractorMode;
        }
    }

    repaint();
}

void BloomChamberComponent::mouseDrag(const juce::MouseEvent& e)
{
    if (isDragging)
    {
        mousePosition = e.position;

        if (isXYControlActive)
        {
            // XY Control mode
            xyPosition = screenToNormalizedXY(e.position);
            xyTrail.add(xyPosition);

            // Limit trail length
            if (xyTrail.size() > maxXYTrailPoints)
                xyTrail.remove(0);

            updateXYParameters();
        }
        else
        {
            // Particle interaction mode
            float dragDistance = dragStartPosition.getDistanceFrom(mousePosition);
            interactionStrength = juce::jlimit(0.1f, 2.0f, dragDistance / 50.0f);
        }

        repaint();
    }
}

void BloomChamberComponent::mouseUp(const juce::MouseEvent& e)
{
    isDragging = false;
    isXYControlActive = false;
    interactionStrength = 1.0f;
    repaint();
}

void BloomChamberComponent::mouseMove(const juce::MouseEvent& e)
{
    mousePosition = e.position;

    // Only repaint if we're showing interaction effects
    if (isDragging)
    {
        repaint();
    }
}

void BloomChamberComponent::timerCallback()
{
    // Update animation time
    animationTime += 0.033f; // Approximately 30 FPS

    // Skip frames for performance if needed
    ++frameSkipCounter;
    if (frameSkipCounter <= maxFrameSkip)
    {
        return;
    }
    frameSkipCounter = 0;

    // Only repaint if there are active grains or we're interacting
    if (grainGenerator.getActiveGrainCount() > 0 || isDragging)
    {
        repaint();
    }
}

juce::Colour BloomChamberComponent::getParticleColor(float age, float velocity, float pitch) const
{
    // Create color based on particle properties
    float hue = std::fmod(pitch * 360.0f + age * 60.0f, 360.0f);
    float saturation = 0.6f + 0.4f * velocity;
    float brightness = 0.4f + 0.6f * velocity;

    return juce::Colour::fromHSV(hue / 360.0f, saturation, brightness, 1.0f);
}

float BloomChamberComponent::getParticleAlpha(float age, float lifespan) const
{
    // Fade in and out over particle lifetime
    float normalizedAge = age / lifespan;

    if (normalizedAge < 0.1f)
    {
        // Fade in
        return normalizedAge / 0.1f * 0.8f;
    }
    else if (normalizedAge > 0.9f)
    {
        // Fade out
        return (1.0f - normalizedAge) / 0.1f * 0.8f;
    }
    else
    {
        // Full opacity
        return 0.8f;
    }
}

// XY Control methods
void BloomChamberComponent::updateXYParameters()
{
    if (!onXYParameterChange)
        return;

    // X轴参数映射 (0.0 = 左, 1.0 = 右)
    float xValue = xyPosition.x;
    onXYParameterChange("rate", 0.1f + xValue * 9.9f);        // Rate: 0.1 - 10.0
    onXYParameterChange("grainSize", 10.0f + xValue * 190.0f); // Size: 10 - 200ms
    onXYParameterChange("pitch", -12.0f + xValue * 24.0f);     // Pitch: -12 to +12 semitones

    // Y轴参数映射 (0.0 = 下, 1.0 = 上)
    float yValue = xyPosition.y;
    onXYParameterChange("density", 0.1f + yValue * 0.9f);      // Density: 0.1 - 1.0
    onXYParameterChange("texture", yValue);                    // Texture: 0.0 - 1.0
    onXYParameterChange("spread", yValue);                     // Spread: 0.0 - 1.0
}

void BloomChamberComponent::drawXYGrid(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat().reduced(20.0f);

    g.setColour(juce::Colours::white.withAlpha(0.1f));

    // 绘制4x4网格
    for (int i = 1; i < 4; ++i)
    {
        float x = bounds.getX() + bounds.getWidth() * i / 4.0f;
        g.drawVerticalLine(static_cast<int>(x), bounds.getY(), bounds.getBottom());

        float y = bounds.getY() + bounds.getHeight() * i / 4.0f;
        g.drawHorizontalLine(static_cast<int>(y), bounds.getX(), bounds.getRight());
    }

    // Center crosshairs
    g.setColour(juce::Colours::white.withAlpha(0.15f));
    float centerX = bounds.getCentreX();
    float centerY = bounds.getCentreY();
    g.drawVerticalLine(static_cast<int>(centerX), bounds.getY(), bounds.getBottom());
    g.drawHorizontalLine(static_cast<int>(centerY), bounds.getX(), bounds.getRight());

    // Parameter labels
    g.setColour(juce::Colours::white.withAlpha(0.5f));
    g.setFont(10.0f);

    // X-axis label
    g.drawText("Rate/Size/Pitch →", bounds.getX(), bounds.getBottom() + 5,
               bounds.getWidth(), 15, juce::Justification::centred);

    // Y-axis label (rotated)
    g.saveState();
    g.addTransform(juce::AffineTransform::rotation(-juce::MathConstants<float>::halfPi,
                                                   bounds.getX() - 15, bounds.getCentreY()));
    g.drawText("Density/Texture/Spread", -50, -5, 100, 15, juce::Justification::centred);
    g.restoreState();
}

void BloomChamberComponent::drawXYControlPoint(juce::Graphics& g)
{
    auto screenPos = normalizedToScreenXY(xyPosition);

    // Outer glow
    float glowSize = isXYControlActive ? 20.0f : 15.0f;
    g.setColour(juce::Colour::fromRGBA(255, 200, 100, 60)); // Warm orange
    g.fillEllipse(screenPos.x - glowSize/2, screenPos.y - glowSize/2, glowSize, glowSize);

    // Main control point
    g.setColour(isXYControlActive ?
               juce::Colour::fromRGBA(255, 220, 120, 255) :
               juce::Colour::fromRGBA(255, 200, 100, 200));
    float pointSize = isXYControlActive ? 12.0f : 10.0f;
    g.fillEllipse(screenPos.x - pointSize/2, screenPos.y - pointSize/2, pointSize, pointSize);

    // Center point
    g.setColour(juce::Colours::white);
    g.fillEllipse(screenPos.x - 2, screenPos.y - 2, 4, 4);

    // Crosshair indicator
    if (isXYControlActive)
    {
        g.setColour(juce::Colours::white.withAlpha(0.6f));
        auto bounds = getLocalBounds().toFloat().reduced(20.0f);

        // Vertical line
        g.drawVerticalLine(static_cast<int>(screenPos.x), bounds.getY(), bounds.getBottom());
        // Horizontal line
        g.drawHorizontalLine(static_cast<int>(screenPos.y), bounds.getX(), bounds.getRight());
    }
}

void BloomChamberComponent::drawXYTrail(juce::Graphics& g)
{
    if (xyTrail.size() < 2) return;

    for (int i = 1; i < xyTrail.size(); ++i)
    {
        float alpha = static_cast<float>(i) / static_cast<float>(xyTrail.size()) * 0.4f;
        g.setColour(juce::Colour::fromRGBA(255, 200, 100, static_cast<juce::uint8>(alpha * 255)));

        auto p1 = normalizedToScreenXY(xyTrail[i-1]);
        auto p2 = normalizedToScreenXY(xyTrail[i]);
        g.drawLine(p1.x, p1.y, p2.x, p2.y, 2.0f);
    }
}

juce::Point<float> BloomChamberComponent::screenToNormalizedXY(juce::Point<float> screenPoint) const
{
    auto bounds = getLocalBounds().toFloat().reduced(20.0f);

    float x = juce::jlimit(0.0f, 1.0f, (screenPoint.x - bounds.getX()) / bounds.getWidth());
    float y = juce::jlimit(0.0f, 1.0f, 1.0f - (screenPoint.y - bounds.getY()) / bounds.getHeight());

    return {x, y};
}

juce::Point<float> BloomChamberComponent::normalizedToScreenXY(juce::Point<float> normalizedPoint) const
{
    auto bounds = getLocalBounds().toFloat().reduced(20.0f);

    float x = bounds.getX() + normalizedPoint.x * bounds.getWidth();
    float y = bounds.getY() + (1.0f - normalizedPoint.y) * bounds.getHeight();

    return {x, y};
}
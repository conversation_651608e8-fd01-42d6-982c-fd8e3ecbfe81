/*
  ==============================================================================

    XYControlPad.cpp
    Created: 21 May 2023
    Author:  AuraBloom Team

    XY控制板的实现 - 多参数同时控制

  ==============================================================================
*/

#include "XYControlPad.h"

XYControlPad::XYControlPad()
{
    setInterceptsMouseClicks(true, true);

    // 设置默认的参数映射
    addXAxisParameter("rate", 0.1f, 10.0f);      // X轴: 颗粒速率
    addXAxisParameter("grainSize", 10.0f, 200.0f); // X轴: 颗粒大小
    addXAxisParameter("pitch", -12.0f, 12.0f);    // X轴: 音高偏移

    addYAxisParameter("density", 0.1f, 1.0f);     // Y轴: 颗粒密度
    addYAxisParameter("texture", 0.0f, 1.0f);     // Y轴: 纹理强度
    addYAxisParameter("spread", 0.0f, 1.0f);      // Y轴: 立体声展开

    // 初始化参数值
    updateParametersFromPosition();
}

XYControlPad::~XYControlPad()
{
}

void XYControlPad::paint(juce::Graphics& g)
{
    drawBackground(g);

    if (showGrid)
        drawGrid(g);

    drawParameterLabels(g);

    if (showTrail)
        drawTrail(g);

    drawControlPoint(g);
    drawParameterValues(g);
}

void XYControlPad::drawBackground(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // Create dark gradient background
    juce::ColourGradient gradient(
        juce::Colour(25, 25, 35),    // Dark gray-blue
        juce::Point<float>(bounds.getCentreX(), bounds.getY()),
        juce::Colour(35, 25, 45),    // Dark purple
        juce::Point<float>(bounds.getCentreX(), bounds.getBottom()),
        false
    );

    g.setGradientFill(gradient);
    g.fillRoundedRectangle(bounds, 8.0f);

    // Border
    g.setColour(juce::Colour(80, 80, 100));
    g.drawRoundedRectangle(bounds, 8.0f, 2.0f);
}

void XYControlPad::drawGrid(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat().reduced(10.0f);

    g.setColour(juce::Colours::white.withAlpha(0.1f));

    // Vertical grid lines
    for (int i = 1; i < 4; ++i)
    {
        float x = bounds.getX() + bounds.getWidth() * i / 4.0f;
        g.drawVerticalLine(static_cast<int>(x), bounds.getY(), bounds.getBottom());
    }

    // Horizontal grid lines
    for (int i = 1; i < 4; ++i)
    {
        float y = bounds.getY() + bounds.getHeight() * i / 4.0f;
        g.drawHorizontalLine(static_cast<int>(y), bounds.getX(), bounds.getRight());
    }

    // Center crosshairs
    g.setColour(juce::Colours::white.withAlpha(0.2f));
    float centerX = bounds.getCentreX();
    float centerY = bounds.getCentreY();
    g.drawVerticalLine(static_cast<int>(centerX), bounds.getY(), bounds.getBottom());
    g.drawHorizontalLine(static_cast<int>(centerY), bounds.getX(), bounds.getRight());
}

void XYControlPad::drawParameterLabels(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    g.setColour(juce::Colours::white.withAlpha(0.7f));
    g.setFont(11.0f);

    // X-axis labels (bottom)
    juce::String xLabels = "X-axis: ";
    for (size_t i = 0; i < xAxisMappings.size(); ++i)
    {
        if (i > 0) xLabels += ", ";
        xLabels += xAxisMappings[i].parameterId;
    }
    g.drawText(xLabels, bounds.removeFromBottom(20).reduced(5, 0),
               juce::Justification::centredLeft);

    // Y-axis labels (left side, rotated 90 degrees)
    juce::String yLabels = "Y-axis: ";
    for (size_t i = 0; i < yAxisMappings.size(); ++i)
    {
        if (i > 0) yLabels += ", ";
        yLabels += yAxisMappings[i].parameterId;
    }

    g.saveState();
    g.addTransform(juce::AffineTransform::rotation(-juce::MathConstants<float>::halfPi,
                                                   bounds.getX() + 10, bounds.getCentreY()));
    g.drawText(yLabels, 0, 0, 100, 20, juce::Justification::centredLeft);
    g.restoreState();
}

void XYControlPad::drawTrail(juce::Graphics& g)
{
    if (trailPoints.size() < 2) return;

    juce::Path trailPath;
    bool firstPoint = true;

    for (size_t i = 0; i < trailPoints.size(); ++i)
    {
        auto screenPoint = normalizedToScreen(trailPoints[i]);

        if (firstPoint)
        {
            trailPath.startNewSubPath(screenPoint);
            firstPoint = false;
        }
        else
        {
            trailPath.lineTo(screenPoint);
        }
    }

    // Gradient transparency trail
    for (size_t i = 1; i < trailPoints.size(); ++i)
    {
        float alpha = static_cast<float>(i) / static_cast<float>(trailPoints.size()) * 0.5f;
        g.setColour(juce::Colour::fromRGBA(100, 200, 255, static_cast<juce::uint8>(alpha * 255)));

        auto p1 = normalizedToScreen(trailPoints[i-1]);
        auto p2 = normalizedToScreen(trailPoints[i]);
        g.drawLine(p1.x, p1.y, p2.x, p2.y, 2.0f);
    }
}

void XYControlPad::drawControlPoint(juce::Graphics& g)
{
    auto screenPos = normalizedToScreen(currentPosition);

    // Outer glow
    g.setColour(juce::Colour::fromRGBA(100, 200, 255, 76));
    g.fillEllipse(screenPos.x - controlPointSize, screenPos.y - controlPointSize,
                  controlPointSize * 2, controlPointSize * 2);

    // Main control point
    g.setColour(isDragging ? juce::Colour(150, 220, 255) : juce::Colour(100, 200, 255));
    g.fillEllipse(screenPos.x - controlPointSize * 0.6f, screenPos.y - controlPointSize * 0.6f,
                  controlPointSize * 1.2f, controlPointSize * 1.2f);

    // Center point
    g.setColour(juce::Colours::white);
    g.fillEllipse(screenPos.x - 2, screenPos.y - 2, 4, 4);
}

void XYControlPad::drawParameterValues(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    g.setColour(juce::Colours::white.withAlpha(0.8f));
    g.setFont(10.0f);

    // Display current coordinates
    juce::String coordText = juce::String::formatted("X: %.2f, Y: %.2f",
                                                     currentPosition.x, currentPosition.y);
    g.drawText(coordText, bounds.getX() + 5, bounds.getY() + 5, 100, 15,
               juce::Justification::topLeft);
}

void XYControlPad::resized()
{
    // No special handling needed when component size changes
}

void XYControlPad::mouseDown(const juce::MouseEvent& e)
{
    isDragging = true;
    currentPosition = screenToNormalized(e.position);

    // Clear trail and add starting point
    trailPoints.clear();
    trailPoints.push_back(currentPosition);

    updateParametersFromPosition();
    repaint();
}

void XYControlPad::mouseDrag(const juce::MouseEvent& e)
{
    if (isDragging)
    {
        currentPosition = screenToNormalized(e.position);

        // Add trail point
        trailPoints.push_back(currentPosition);
        if (trailPoints.size() > maxTrailPoints)
        {
            trailPoints.erase(trailPoints.begin());
        }

        updateParametersFromPosition();

        if (onPositionChange)
            onPositionChange(currentPosition.x, currentPosition.y);

        repaint();
    }
}

void XYControlPad::mouseUp(const juce::MouseEvent& e)
{
    isDragging = false;
    repaint();
}

// Parameter mapping management
void XYControlPad::addXAxisParameter(const juce::String& parameterId, float minValue, float maxValue)
{
    xAxisMappings.emplace_back(parameterId, minValue, maxValue);
}

void XYControlPad::addYAxisParameter(const juce::String& parameterId, float minValue, float maxValue)
{
    yAxisMappings.emplace_back(parameterId, minValue, maxValue);
}

void XYControlPad::removeParameter(const juce::String& parameterId)
{
    // Remove from X-axis mappings
    xAxisMappings.erase(
        std::remove_if(xAxisMappings.begin(), xAxisMappings.end(),
                      [&parameterId](const ParameterMapping& mapping) {
                          return mapping.parameterId == parameterId;
                      }),
        xAxisMappings.end());

    // Remove from Y-axis mappings
    yAxisMappings.erase(
        std::remove_if(yAxisMappings.begin(), yAxisMappings.end(),
                      [&parameterId](const ParameterMapping& mapping) {
                          return mapping.parameterId == parameterId;
                      }),
        yAxisMappings.end());
}

void XYControlPad::clearAllMappings()
{
    xAxisMappings.clear();
    yAxisMappings.clear();
}

void XYControlPad::setXYPosition(float x, float y)
{
    currentPosition.x = juce::jlimit(0.0f, 1.0f, x);
    currentPosition.y = juce::jlimit(0.0f, 1.0f, y);
    updateParametersFromPosition();
    repaint();
}

float XYControlPad::getParameterValue(const juce::String& parameterId) const
{
    // Search in X-axis mappings
    for (const auto& mapping : xAxisMappings)
    {
        if (mapping.parameterId == parameterId)
            return mapping.currentValue;
    }

    // Search in Y-axis mappings
    for (const auto& mapping : yAxisMappings)
    {
        if (mapping.parameterId == parameterId)
            return mapping.currentValue;
    }

    return 0.0f; // Parameter not found
}

// Internal methods
void XYControlPad::updateParametersFromPosition()
{
    // Update X-axis parameters
    updateParameterMapping(xAxisMappings, currentPosition.x);

    // Update Y-axis parameters
    updateParameterMapping(yAxisMappings, currentPosition.y);
}

void XYControlPad::updateParameterMapping(std::vector<ParameterMapping>& mappings, float normalizedValue)
{
    for (auto& mapping : mappings)
    {
        if (mapping.isActive)
        {
            mapping.currentValue = mapping.minValue +
                                 (mapping.maxValue - mapping.minValue) * normalizedValue;

            if (onParameterChange)
                onParameterChange(mapping.parameterId, mapping.currentValue);
        }
    }
}

juce::Point<float> XYControlPad::screenToNormalized(juce::Point<float> screenPoint) const
{
    auto bounds = getLocalBounds().toFloat().reduced(10.0f);

    float x = juce::jlimit(0.0f, 1.0f, (screenPoint.x - bounds.getX()) / bounds.getWidth());
    float y = juce::jlimit(0.0f, 1.0f, 1.0f - (screenPoint.y - bounds.getY()) / bounds.getHeight());

    return {x, y};
}

juce::Point<float> XYControlPad::normalizedToScreen(juce::Point<float> normalizedPoint) const
{
    auto bounds = getLocalBounds().toFloat().reduced(10.0f);

    float x = bounds.getX() + normalizedPoint.x * bounds.getWidth();
    float y = bounds.getY() + (1.0f - normalizedPoint.y) * bounds.getHeight();

    return {x, y};
}

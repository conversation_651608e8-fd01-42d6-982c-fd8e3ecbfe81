// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		516691A5770BC0B6624ED333 /* AuraBloom - All */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = E28518ED7E5BEE969BBE425B;
			buildPhases = (
			);
			dependencies = (
				9A89C814310C0C215F5B5C49,
				C90ECAC542EC0B9EC3C0EE02,
				3A30F4D903C189B3AA332064,
				EF555A1F43FDD65ECC3904F9,
			);
			name = "AuraBloom - All";
			productName = AuraBloom;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		02884CA29281A751E889F69A /* juce_VST3ManifestHelper.mm */ = {isa = PBXBuildFile; fileRef = 974DAA4DEACEAED721DEE3EC; settings = { COMPILER_FLAGS = "-fobjc-arc -w -DJUCE_SKIP_PRECOMPILED_HEADER"; }; };
		06FA3F1AE5D96B0D0E3E4EB0 /* Metal.framework */ = {isa = PBXBuildFile; fileRef = EEECF9BA18A6E81DB3BFABA4; settings = { ATTRIBUTES = (Weak, ); }; };
		085CB5CE77BC38E064C8A18E /* Emitter.cpp */ = {isa = PBXBuildFile; fileRef = 9F7231CD31733BC12553BA3C; };
		0988A3D48CA9D594F078311C /* include_juce_opengl.mm */ = {isa = PBXBuildFile; fileRef = D2D67EEA2BC55C62A3072213; };
		0BAFDF43895A9805CC767162 /* Cocoa.framework */ = {isa = PBXBuildFile; fileRef = A8D5042BE73F978E6408081B; };
		12B2E10A73C69A80541602D0 /* include_juce_javascript.cpp */ = {isa = PBXBuildFile; fileRef = EFC412C9AC94A2BCA03ABE5B; };
		13E479489711E992EE5939A1 /* EnvelopeComponent.cpp */ = {isa = PBXBuildFile; fileRef = A2253D442374D45BE7920BA3; };
		1D7663C8A2B72D7CEDA50E85 /* LFOComponent.cpp */ = {isa = PBXBuildFile; fileRef = 616AD6AF72738B5C6719C9A1; };
		2407AC4D5EF85F1EDAD07C94 /* Shared Code */ = {isa = PBXBuildFile; fileRef = 568CF448626993FEBB86509C; };
		26F0F79FF12A1988CB5773D1 /* ParameterModulationTarget.cpp */ = {isa = PBXBuildFile; fileRef = 721FFAED6EBA9FF2E7E1BE9A; };
		281C7AA2CA9B4D81823AE022 /* include_juce_gui_basics.mm */ = {isa = PBXBuildFile; fileRef = 48A8D4F1FF41C31F8C24916B; };
		2AF00C9F6C9F11E2ACE4E120 /* PluginEditor.cpp */ = {isa = PBXBuildFile; fileRef = FE6940B8CDE4BBC3C8E402E5; };
		2B6C6BCBA54EA61E9BADE946 /* ModulationSourceComponent.cpp */ = {isa = PBXBuildFile; fileRef = 73EC4A329260AB4BA962551A; };
		30760C49A1625FAD15D61651 /* include_juce_audio_processors_ara.cpp */ = {isa = PBXBuildFile; fileRef = E9AE260B5826495B625DE856; };
		31266DEFF4BD3937F72A2F32 /* ModulationTarget.cpp */ = {isa = PBXBuildFile; fileRef = 4D58FAEE9EB2C38E7E975C6E; };
		3131EF3454491B2D04948E79 /* Envelope.cpp */ = {isa = PBXBuildFile; fileRef = 25661989A53E1D912513E1B2; };
		34C45D14B6EA3B3A0D11FB0E /* ParticleSystem.cpp */ = {isa = PBXBuildFile; fileRef = F6C35D2D472C33ACDC3E0C6D; };
		34F159090ED4818C0B97DE48 /* XYControlPad.cpp */ = {isa = PBXBuildFile; fileRef = 2D8CFA670B8A34F707CE2E0E; };
		3A6C6B235B18D311994630E5 /* PerformanceMonitor.cpp */ = {isa = PBXBuildFile; fileRef = 411E413E8BCC15E90E1391D1; };
		3AC927120D778BD7D6232ABE /* MacroControlKnob.cpp */ = {isa = PBXBuildFile; fileRef = 9048707A91911B226276677C; };
		41A17867C6FFCF23FB1153F4 /* DiscRecording.framework */ = {isa = PBXBuildFile; fileRef = 60BB0B870CB93C92EDDF98D8; };
		4426BE35E11C23D38A631D9E /* QuartzCore.framework */ = {isa = PBXBuildFile; fileRef = 93469C8A2EB3B58DE3BC859E; };
		47B0D46DE5693EA5C1608216 /* ModulationMatrix.cpp */ = {isa = PBXBuildFile; fileRef = C6EA8A059154CFEEC6B42A43; };
		50A0C4E1F3B1A45D4B38153B /* AudioToolbox.framework */ = {isa = PBXBuildFile; fileRef = C9ED7BB29FD4340010B5FFAC; };
		51826A6DCD661F39BE7B1DDE /* MetalKit.framework */ = {isa = PBXBuildFile; fileRef = EB4910129791947198A07E71; settings = { ATTRIBUTES = (Weak, ); }; };
		5554EFFFEDF827FF7F2B938D /* include_juce_core_CompilationTime.cpp */ = {isa = PBXBuildFile; fileRef = 6699363EB4CC7A1A13D8F24C; };
		5A157DC93AD3AB071B068FD7 /* include_juce_events.mm */ = {isa = PBXBuildFile; fileRef = 08C83A456C9F45E4B25B5BE7; };
		5A7CD5921E4DA250C0016C91 /* AudioUnit.framework */ = {isa = PBXBuildFile; fileRef = BD02B2AA7C2A48AF1D8C8994; };
		5B1D564A7FB2150E14F1BB7A /* VST3 */ = {isa = PBXBuildFile; fileRef = F9D23F95622E2C8B86734CE7; };
		5FA71FD9ADB72C1DBFFCCEE0 /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXBuildFile; fileRef = 868AA0D02FFB870A50878526; };
		602DFFCE42CB2B9ADB17AA7D /* CoreAudio.framework */ = {isa = PBXBuildFile; fileRef = 0E1AF2A5ACF4831C99DA75AA; };
		60F17317DE8027B281449AAC /* RecentFilesMenuTemplate.nib */ = {isa = PBXBuildFile; fileRef = F29AEBE4391B30D09F4EE00C; };
		62C626F79B9454A69BF0DEE3 /* Security.framework */ = {isa = PBXBuildFile; fileRef = 404CE0BFAFECCA2C07A1CED0; };
		665D700DB3EDBFD328C28C25 /* include_juce_dsp.mm */ = {isa = PBXBuildFile; fileRef = 4AF0810ED05BC74C38E6D906; };
		6F1962120F68FF814CC306D8 /* ModulationSource.cpp */ = {isa = PBXBuildFile; fileRef = 21471F0C65A747972FB96C49; };
		6F88C6F8AAC07C6202D5C0B4 /* BloomChamberComponent.cpp */ = {isa = PBXBuildFile; fileRef = 4D58C9173D7CCB406552BBC3; };
		6FC8D356862CAAF459C332D8 /* PluginProcessor.cpp */ = {isa = PBXBuildFile; fileRef = E0973D65C324CEB1BAE442AA; };
		7049F4422D75E70238527780 /* include_juce_audio_plugin_client_Standalone.cpp */ = {isa = PBXBuildFile; fileRef = AD8B16248A561310D9A5A52B; };
		7981AE50F22167766049349F /* include_juce_graphics.mm */ = {isa = PBXBuildFile; fileRef = F1598BF2DF23C60E456F5318; };
		7D0C6B9467221555C20D9046 /* include_juce_osc.cpp */ = {isa = PBXBuildFile; fileRef = 16570337C74E2E4739B8BA25; };
		7FF13CD4EA063994674CE3CB /* include_juce_audio_plugin_client_ARA.cpp */ = {isa = PBXBuildFile; fileRef = D303775627490EC796169B93; };
		80F8C61EF1EEFDF8ECDA7B84 /* include_juce_core.mm */ = {isa = PBXBuildFile; fileRef = 908C3088365D80179B896543; };
		81369D5288DEFAE5CF4E24CD /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXBuildFile; fileRef = D7F85B6432D7B8D4FDEC9B12; };
		856795A7F1A2F281288A0302 /* Accelerate.framework */ = {isa = PBXBuildFile; fileRef = ED6D9A401C61F054BB1BD7C2; };
		872A09E6955670ADD2CA0CE8 /* CoreMIDI.framework */ = {isa = PBXBuildFile; fileRef = CB2E64CB6A8E3456623888A9; };
		8740BA28BB317D88BB619F03 /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXBuildFile; fileRef = 03449502B843566955E501A9; };
		8B9CE2AC3A68EB6CFE8FEFE5 /* include_juce_audio_utils.mm */ = {isa = PBXBuildFile; fileRef = 4293B3F8D7AB3C4F60E5FE6D; };
		9BCA378A38B53E26430782DF /* include_juce_gui_extra.mm */ = {isa = PBXBuildFile; fileRef = F0C119A4900CDD4B906DE49D; };
		A4977406877BC634B8CBA122 /* IOKit.framework */ = {isa = PBXBuildFile; fileRef = 6FD8DB6D45F3DCFDCCB310E7; };
		B5884CAEF2FA168823493E36 /* include_juce_audio_formats.mm */ = {isa = PBXBuildFile; fileRef = C320DB515A51A03E4D8994C1; };
		B6A41C00F9FD5798FC06EE0C /* include_juce_data_structures.mm */ = {isa = PBXBuildFile; fileRef = 2452132BF82D51F543CC1724; };
		BB83188F7C0EE5765B8BAFDC /* Particle.cpp */ = {isa = PBXBuildFile; fileRef = 42BC727F20E66A7E93E8FE0B; };
		BCC12C5F7EB4902E3DAAA77A /* CoreAudioKit.framework */ = {isa = PBXBuildFile; fileRef = 36C7116EF4C20979C77C5D3E; };
		C77DA9DC2B155D6BF55A48BC /* LFO.cpp */ = {isa = PBXBuildFile; fileRef = B89CADA2ED9D0916075D5B09; };
		C7ADDCE7967C3832971BEE3A /* MacroControlPanel.cpp */ = {isa = PBXBuildFile; fileRef = F5237872170576E3AEAA5610; };
		CC34E7B039386D8B715C2063 /* include_juce_audio_processors.mm */ = {isa = PBXBuildFile; fileRef = 0637A923AEEA196D33E23530; };
		CDAA4CC4906435FAE022A9FD /* Standalone Plugin */ = {isa = PBXBuildFile; fileRef = C6CB0D2D5996E9826C4F7DD1; };
		D0F248C2141B6F15BC89AE89 /* VST3 Manifest Helper */ = {isa = PBXBuildFile; fileRef = 72B94E2C27EB2B1251881CCC; };
		D6E268842908E141D37699A6 /* WebKit.framework */ = {isa = PBXBuildFile; fileRef = 0509A229851B05A89BE3BF9F; };
		D6EE4C4446507BE27B9D01C0 /* AudioBufferManager.cpp */ = {isa = PBXBuildFile; fileRef = 99BC596407ED8C1440AD44B0; };
		DA5EFA4628FD7351CBB41503 /* include_juce_audio_plugin_client_VST3.mm */ = {isa = PBXBuildFile; fileRef = 2DA473EBC955467DA8AC961E; };
		E393D94E4209642AAB121BA7 /* include_juce_audio_devices.mm */ = {isa = PBXBuildFile; fileRef = E3FFEB0501F74E3B1BE40E6D; };
		E486B33A167995190B79CE35 /* Foundation.framework */ = {isa = PBXBuildFile; fileRef = F33F69952CD4E9FEE39F559C; };
		E7A499297F940B0AC7142E5D /* OpenGL.framework */ = {isa = PBXBuildFile; fileRef = 1234510402282F87776525ED; };
		E86EAEFBA84B15CF05713FC9 /* include_juce_audio_basics.mm */ = {isa = PBXBuildFile; fileRef = E3E1E19863D2BB2F71482CBA; };
		FA8B5697AE23312A68F36B75 /* GrainGenerator.cpp */ = {isa = PBXBuildFile; fileRef = C71370DC400D31549CE99450; };
		FC75F3F4A7DC40B9E74DBF8B /* ModulationMatrixComponent.cpp */ = {isa = PBXBuildFile; fileRef = 4F2BE73A9E6F665BB9B58ED6; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0204C27C414D31A2BCA84E97 /* juce_audio_formats */ /* juce_audio_formats */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_formats; path = "~/JUCE/modules/juce_audio_formats"; sourceTree = "<absolute>"; };
		03449502B843566955E501A9 /* include_juce_graphics_Sheenbidi.c */ /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = include_juce_graphics_Sheenbidi.c; path = ../../JuceLibraryCode/include_juce_graphics_Sheenbidi.c; sourceTree = SOURCE_ROOT; };
		0359B6C49F41ED487C08C746 /* juce_events */ /* juce_events */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_events; path = "~/JUCE/modules/juce_events"; sourceTree = "<absolute>"; };
		0509A229851B05A89BE3BF9F /* WebKit.framework */ /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		0637A923AEEA196D33E23530 /* include_juce_audio_processors.mm */ /* include_juce_audio_processors.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_processors.mm; path = ../../JuceLibraryCode/include_juce_audio_processors.mm; sourceTree = SOURCE_ROOT; };
		08C83A456C9F45E4B25B5BE7 /* include_juce_events.mm */ /* include_juce_events.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_events.mm; path = ../../JuceLibraryCode/include_juce_events.mm; sourceTree = SOURCE_ROOT; };
		0E1AF2A5ACF4831C99DA75AA /* CoreAudio.framework */ /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		1234510402282F87776525ED /* OpenGL.framework */ /* OpenGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGL.framework; path = System/Library/Frameworks/OpenGL.framework; sourceTree = SDKROOT; };
		15A3750971D43E7F215E7F01 /* PluginEditor.h */ /* PluginEditor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PluginEditor.h; path = ../../Source/PluginEditor.h; sourceTree = SOURCE_ROOT; };
		16570337C74E2E4739B8BA25 /* include_juce_osc.cpp */ /* include_juce_osc.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_osc.cpp; path = ../../JuceLibraryCode/include_juce_osc.cpp; sourceTree = SOURCE_ROOT; };
		21471F0C65A747972FB96C49 /* ModulationSource.cpp */ /* ModulationSource.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ModulationSource.cpp; path = ../../Source/ModulationSource.cpp; sourceTree = SOURCE_ROOT; };
		2452132BF82D51F543CC1724 /* include_juce_data_structures.mm */ /* include_juce_data_structures.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_data_structures.mm; path = ../../JuceLibraryCode/include_juce_data_structures.mm; sourceTree = SOURCE_ROOT; };
		25661989A53E1D912513E1B2 /* Envelope.cpp */ /* Envelope.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Envelope.cpp; path = ../../Source/Envelope.cpp; sourceTree = SOURCE_ROOT; };
		258DAA20F71E92EA208949B5 /* ForceField.h */ /* ForceField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ForceField.h; path = ../../Source/ForceField.h; sourceTree = SOURCE_ROOT; };
		28E12ECDE889A211EDC30F14 /* juce_audio_devices */ /* juce_audio_devices */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_devices; path = "~/JUCE/modules/juce_audio_devices"; sourceTree = "<absolute>"; };
		29B43323580FA73FB9ECBAEA /* juce_javascript */ /* juce_javascript */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_javascript; path = "~/JUCE/modules/juce_javascript"; sourceTree = "<absolute>"; };
		2B6F1C59C0894D84C4181F03 /* XYControlPad.h */ /* XYControlPad.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = XYControlPad.h; path = ../../Source/XYControlPad.h; sourceTree = SOURCE_ROOT; };
		2D70D90EEE1215CCDC737F77 /* GrainGenerator.h */ /* GrainGenerator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = GrainGenerator.h; path = ../../Source/GrainGenerator.h; sourceTree = SOURCE_ROOT; };
		2D8CFA670B8A34F707CE2E0E /* XYControlPad.cpp */ /* XYControlPad.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = XYControlPad.cpp; path = ../../Source/XYControlPad.cpp; sourceTree = SOURCE_ROOT; };
		2DA473EBC955467DA8AC961E /* include_juce_audio_plugin_client_VST3.mm */ /* include_juce_audio_plugin_client_VST3.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_plugin_client_VST3.mm; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_VST3.mm; sourceTree = SOURCE_ROOT; };
		2EDA7298D8717F131196223A /* Particle.h */ /* Particle.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = Particle.h; path = ../../Source/Particle.h; sourceTree = SOURCE_ROOT; };
		36B9C4C5F9454F323E816271 /* juce_gui_extra */ /* juce_gui_extra */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_extra; path = "~/JUCE/modules/juce_gui_extra"; sourceTree = "<absolute>"; };
		36C7116EF4C20979C77C5D3E /* CoreAudioKit.framework */ /* CoreAudioKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioKit.framework; path = System/Library/Frameworks/CoreAudioKit.framework; sourceTree = SDKROOT; };
		38D24ED25DDCB77B4358F0E6 /* juce_audio_plugin_client */ /* juce_audio_plugin_client */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_plugin_client; path = "~/JUCE/modules/juce_audio_plugin_client"; sourceTree = "<absolute>"; };
		3BCF7665954C2AA7EAB0B28E /* juce_audio_processors */ /* juce_audio_processors */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_processors; path = "~/JUCE/modules/juce_audio_processors"; sourceTree = "<absolute>"; };
		404CE0BFAFECCA2C07A1CED0 /* Security.framework */ /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		411E413E8BCC15E90E1391D1 /* PerformanceMonitor.cpp */ /* PerformanceMonitor.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = PerformanceMonitor.cpp; path = ../../Source/PerformanceMonitor.cpp; sourceTree = SOURCE_ROOT; };
		427A4BC3A82CEA8CC1220E2E /* PluginProcessor.h */ /* PluginProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PluginProcessor.h; path = ../../Source/PluginProcessor.h; sourceTree = SOURCE_ROOT; };
		4293B3F8D7AB3C4F60E5FE6D /* include_juce_audio_utils.mm */ /* include_juce_audio_utils.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_utils.mm; path = ../../JuceLibraryCode/include_juce_audio_utils.mm; sourceTree = SOURCE_ROOT; };
		42BC727F20E66A7E93E8FE0B /* Particle.cpp */ /* Particle.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Particle.cpp; path = ../../Source/Particle.cpp; sourceTree = SOURCE_ROOT; };
		48A8D4F1FF41C31F8C24916B /* include_juce_gui_basics.mm */ /* include_juce_gui_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_basics.mm; path = ../../JuceLibraryCode/include_juce_gui_basics.mm; sourceTree = SOURCE_ROOT; };
		48F05E2EF772805C0A8C02AA /* Info-Standalone_Plugin.plist */ /* Info-Standalone_Plugin.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-Standalone_Plugin.plist"; path = "Info-Standalone_Plugin.plist"; sourceTree = SOURCE_ROOT; };
		490C7CEE4945037D2D203EC7 /* PerformanceMonitor.h */ /* PerformanceMonitor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PerformanceMonitor.h; path = ../../Source/PerformanceMonitor.h; sourceTree = SOURCE_ROOT; };
		4AF0810ED05BC74C38E6D906 /* include_juce_dsp.mm */ /* include_juce_dsp.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_dsp.mm; path = ../../JuceLibraryCode/include_juce_dsp.mm; sourceTree = SOURCE_ROOT; };
		4D58C9173D7CCB406552BBC3 /* BloomChamberComponent.cpp */ /* BloomChamberComponent.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = BloomChamberComponent.cpp; path = ../../Source/BloomChamberComponent.cpp; sourceTree = SOURCE_ROOT; };
		4D58FAEE9EB2C38E7E975C6E /* ModulationTarget.cpp */ /* ModulationTarget.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ModulationTarget.cpp; path = ../../Source/ModulationTarget.cpp; sourceTree = SOURCE_ROOT; };
		4F2BE73A9E6F665BB9B58ED6 /* ModulationMatrixComponent.cpp */ /* ModulationMatrixComponent.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ModulationMatrixComponent.cpp; path = ../../Source/ModulationMatrixComponent.cpp; sourceTree = SOURCE_ROOT; };
		568CF448626993FEBB86509C /* Shared Code */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libAuraBloom.a; sourceTree = BUILT_PRODUCTS_DIR; };
		584D94E13B152348B1BB191A /* BloomChamberComponent.h */ /* BloomChamberComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = BloomChamberComponent.h; path = ../../Source/BloomChamberComponent.h; sourceTree = SOURCE_ROOT; };
		590FCE69D76373DD57C7735B /* PluginParameters.h */ /* PluginParameters.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PluginParameters.h; path = ../../Source/PluginParameters.h; sourceTree = SOURCE_ROOT; };
		60BB0B870CB93C92EDDF98D8 /* DiscRecording.framework */ /* DiscRecording.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = DiscRecording.framework; path = System/Library/Frameworks/DiscRecording.framework; sourceTree = SDKROOT; };
		616AD6AF72738B5C6719C9A1 /* LFOComponent.cpp */ /* LFOComponent.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = LFOComponent.cpp; path = ../../Source/LFOComponent.cpp; sourceTree = SOURCE_ROOT; };
		6699363EB4CC7A1A13D8F24C /* include_juce_core_CompilationTime.cpp */ /* include_juce_core_CompilationTime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_core_CompilationTime.cpp; path = ../../JuceLibraryCode/include_juce_core_CompilationTime.cpp; sourceTree = SOURCE_ROOT; };
		6D47F0797F8269D5E7225FFD /* LFOComponent.h */ /* LFOComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = LFOComponent.h; path = ../../Source/LFOComponent.h; sourceTree = SOURCE_ROOT; };
		6FD8DB6D45F3DCFDCCB310E7 /* IOKit.framework */ /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		711283609CB8CBE78EA8A54B /* juce_osc */ /* juce_osc */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_osc; path = "~/JUCE/modules/juce_osc"; sourceTree = "<absolute>"; };
		721FFAED6EBA9FF2E7E1BE9A /* ParameterModulationTarget.cpp */ /* ParameterModulationTarget.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ParameterModulationTarget.cpp; path = ../../Source/ParameterModulationTarget.cpp; sourceTree = SOURCE_ROOT; };
		72B94E2C27EB2B1251881CCC /* VST3 Manifest Helper */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = juce_vst3_helper; sourceTree = BUILT_PRODUCTS_DIR; };
		73832FFDF7BA7BF355C9CA05 /* ModulationMatrix.h */ /* ModulationMatrix.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ModulationMatrix.h; path = ../../Source/ModulationMatrix.h; sourceTree = SOURCE_ROOT; };
		73EC4A329260AB4BA962551A /* ModulationSourceComponent.cpp */ /* ModulationSourceComponent.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ModulationSourceComponent.cpp; path = ../../Source/ModulationSourceComponent.cpp; sourceTree = SOURCE_ROOT; };
		740FF311E2E695391B1165A0 /* juce_core */ /* juce_core */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_core; path = "~/JUCE/modules/juce_core"; sourceTree = "<absolute>"; };
		7BC6A9D4580448CFBF5B5E75 /* MacroControlKnob.h */ /* MacroControlKnob.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = MacroControlKnob.h; path = ../../Source/MacroControlKnob.h; sourceTree = SOURCE_ROOT; };
		802576648FFFF7DF3E174990 /* JucePluginDefines.h */ /* JucePluginDefines.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JucePluginDefines.h; path = ../../JuceLibraryCode/JucePluginDefines.h; sourceTree = SOURCE_ROOT; };
		82E94BB9321B6079D33A8DEB /* ParticleSystem.h */ /* ParticleSystem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ParticleSystem.h; path = ../../Source/ParticleSystem.h; sourceTree = SOURCE_ROOT; };
		868AA0D02FFB870A50878526 /* include_juce_audio_processors_lv2_libs.cpp */ /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_lv2_libs.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_lv2_libs.cpp; sourceTree = SOURCE_ROOT; };
		9048707A91911B226276677C /* MacroControlKnob.cpp */ /* MacroControlKnob.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = MacroControlKnob.cpp; path = ../../Source/MacroControlKnob.cpp; sourceTree = SOURCE_ROOT; };
		908C3088365D80179B896543 /* include_juce_core.mm */ /* include_juce_core.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_core.mm; path = ../../JuceLibraryCode/include_juce_core.mm; sourceTree = SOURCE_ROOT; };
		9256A1F6A000F19E237E5F05 /* juce_graphics */ /* juce_graphics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_graphics; path = "~/JUCE/modules/juce_graphics"; sourceTree = "<absolute>"; };
		92E49DC2D9FED8A0227A00BD /* ModulationTarget.h */ /* ModulationTarget.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ModulationTarget.h; path = ../../Source/ModulationTarget.h; sourceTree = SOURCE_ROOT; };
		93469C8A2EB3B58DE3BC859E /* QuartzCore.framework */ /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		95F1CD71ABE1C6E9AAB36B66 /* ModulationSource.h */ /* ModulationSource.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ModulationSource.h; path = ../../Source/ModulationSource.h; sourceTree = SOURCE_ROOT; };
		974DAA4DEACEAED721DEE3EC /* juce_VST3ManifestHelper.mm */ /* juce_VST3ManifestHelper.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = juce_VST3ManifestHelper.mm; path = "$(HOME)/JUCE/modules/juce_audio_plugin_client/VST3/juce_VST3ManifestHelper.mm"; sourceTree = "<absolute>"; };
		99BC596407ED8C1440AD44B0 /* AudioBufferManager.cpp */ /* AudioBufferManager.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = AudioBufferManager.cpp; path = ../../Source/AudioBufferManager.cpp; sourceTree = SOURCE_ROOT; };
		9F7231CD31733BC12553BA3C /* Emitter.cpp */ /* Emitter.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Emitter.cpp; path = ../../Source/Emitter.cpp; sourceTree = SOURCE_ROOT; };
		A12C6C155F85967C01DA3AC2 /* JuceHeader.h */ /* JuceHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JuceHeader.h; path = ../../JuceLibraryCode/JuceHeader.h; sourceTree = SOURCE_ROOT; };
		A1AEF826748837F4ABE7C23A /* Info-VST3.plist */ /* Info-VST3.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-VST3.plist"; path = "Info-VST3.plist"; sourceTree = SOURCE_ROOT; };
		A2253D442374D45BE7920BA3 /* EnvelopeComponent.cpp */ /* EnvelopeComponent.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = EnvelopeComponent.cpp; path = ../../Source/EnvelopeComponent.cpp; sourceTree = SOURCE_ROOT; };
		A8D5042BE73F978E6408081B /* Cocoa.framework */ /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		AA6ED7696921421C989B5D5D /* EnvelopeComponent.h */ /* EnvelopeComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = EnvelopeComponent.h; path = ../../Source/EnvelopeComponent.h; sourceTree = SOURCE_ROOT; };
		AD8B16248A561310D9A5A52B /* include_juce_audio_plugin_client_Standalone.cpp */ /* include_juce_audio_plugin_client_Standalone.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_plugin_client_Standalone.cpp; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_Standalone.cpp; sourceTree = SOURCE_ROOT; };
		B47DA7C6CEDB7C61AB043760 /* AudioBufferManager.h */ /* AudioBufferManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = AudioBufferManager.h; path = ../../Source/AudioBufferManager.h; sourceTree = SOURCE_ROOT; };
		B89CADA2ED9D0916075D5B09 /* LFO.cpp */ /* LFO.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = LFO.cpp; path = ../../Source/LFO.cpp; sourceTree = SOURCE_ROOT; };
		B9CA51E55256908CBAD0DA87 /* Envelope.h */ /* Envelope.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = Envelope.h; path = ../../Source/Envelope.h; sourceTree = SOURCE_ROOT; };
		BD02B2AA7C2A48AF1D8C8994 /* AudioUnit.framework */ /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = System/Library/Frameworks/AudioUnit.framework; sourceTree = SDKROOT; };
		BD771DC007E46D25F73E233E /* juce_gui_basics */ /* juce_gui_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_basics; path = "~/JUCE/modules/juce_gui_basics"; sourceTree = "<absolute>"; };
		C320DB515A51A03E4D8994C1 /* include_juce_audio_formats.mm */ /* include_juce_audio_formats.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_formats.mm; path = ../../JuceLibraryCode/include_juce_audio_formats.mm; sourceTree = SOURCE_ROOT; };
		C6CB0D2D5996E9826C4F7DD1 /* Standalone Plugin */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AuraBloom.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C6EA8A059154CFEEC6B42A43 /* ModulationMatrix.cpp */ /* ModulationMatrix.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ModulationMatrix.cpp; path = ../../Source/ModulationMatrix.cpp; sourceTree = SOURCE_ROOT; };
		C71370DC400D31549CE99450 /* GrainGenerator.cpp */ /* GrainGenerator.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = GrainGenerator.cpp; path = ../../Source/GrainGenerator.cpp; sourceTree = SOURCE_ROOT; };
		C963796A02656D6B59D939FB /* ParameterModulationTarget.h */ /* ParameterModulationTarget.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ParameterModulationTarget.h; path = ../../Source/ParameterModulationTarget.h; sourceTree = SOURCE_ROOT; };
		C9ED7BB29FD4340010B5FFAC /* AudioToolbox.framework */ /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		CB2E64CB6A8E3456623888A9 /* CoreMIDI.framework */ /* CoreMIDI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMIDI.framework; path = System/Library/Frameworks/CoreMIDI.framework; sourceTree = SDKROOT; };
		CFA3941115B23C91DC4202DF /* Emitter.h */ /* Emitter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = Emitter.h; path = ../../Source/Emitter.h; sourceTree = SOURCE_ROOT; };
		D174D520AA11B8C7021488A8 /* juce_data_structures */ /* juce_data_structures */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_data_structures; path = "~/JUCE/modules/juce_data_structures"; sourceTree = "<absolute>"; };
		D2D67EEA2BC55C62A3072213 /* include_juce_opengl.mm */ /* include_juce_opengl.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_opengl.mm; path = ../../JuceLibraryCode/include_juce_opengl.mm; sourceTree = SOURCE_ROOT; };
		D303775627490EC796169B93 /* include_juce_audio_plugin_client_ARA.cpp */ /* include_juce_audio_plugin_client_ARA.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_plugin_client_ARA.cpp; path = ../../JuceLibraryCode/include_juce_audio_plugin_client_ARA.cpp; sourceTree = SOURCE_ROOT; };
		D7F85B6432D7B8D4FDEC9B12 /* include_juce_graphics_Harfbuzz.cpp */ /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_graphics_Harfbuzz.cpp; path = ../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp; sourceTree = SOURCE_ROOT; };
		DB055DE38C17A7B0563E3A79 /* Info-VST3_Manifest_Helper.plist */ /* Info-VST3_Manifest_Helper.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-VST3_Manifest_Helper.plist"; path = "Info-VST3_Manifest_Helper.plist"; sourceTree = SOURCE_ROOT; };
		DB87960214EE673533427D90 /* juce_audio_utils */ /* juce_audio_utils */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_utils; path = "~/JUCE/modules/juce_audio_utils"; sourceTree = "<absolute>"; };
		DF9D003B8C99490B5C7B34F2 /* juce_audio_basics */ /* juce_audio_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_basics; path = "~/JUCE/modules/juce_audio_basics"; sourceTree = "<absolute>"; };
		E0973D65C324CEB1BAE442AA /* PluginProcessor.cpp */ /* PluginProcessor.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = PluginProcessor.cpp; path = ../../Source/PluginProcessor.cpp; sourceTree = SOURCE_ROOT; };
		E3E1E19863D2BB2F71482CBA /* include_juce_audio_basics.mm */ /* include_juce_audio_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_basics.mm; path = ../../JuceLibraryCode/include_juce_audio_basics.mm; sourceTree = SOURCE_ROOT; };
		E3FFEB0501F74E3B1BE40E6D /* include_juce_audio_devices.mm */ /* include_juce_audio_devices.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_devices.mm; path = ../../JuceLibraryCode/include_juce_audio_devices.mm; sourceTree = SOURCE_ROOT; };
		E406DDA831E57FE55D0A4C85 /* AuraBloomLookAndFeel.h */ /* AuraBloomLookAndFeel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = AuraBloomLookAndFeel.h; path = ../../Source/AuraBloomLookAndFeel.h; sourceTree = SOURCE_ROOT; };
		E45116766D28C46A3C57951A /* MacroControlPanel.h */ /* MacroControlPanel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = MacroControlPanel.h; path = ../../Source/MacroControlPanel.h; sourceTree = SOURCE_ROOT; };
		E9AE260B5826495B625DE856 /* include_juce_audio_processors_ara.cpp */ /* include_juce_audio_processors_ara.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_ara.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_ara.cpp; sourceTree = SOURCE_ROOT; };
		EB4910129791947198A07E71 /* MetalKit.framework */ /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		ED6D9A401C61F054BB1BD7C2 /* Accelerate.framework */ /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		EE51CC922DAABBB24C5A933B /* juce_opengl */ /* juce_opengl */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_opengl; path = "~/JUCE/modules/juce_opengl"; sourceTree = "<absolute>"; };
		EEECF9BA18A6E81DB3BFABA4 /* Metal.framework */ /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		EFC412C9AC94A2BCA03ABE5B /* include_juce_javascript.cpp */ /* include_juce_javascript.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_javascript.cpp; path = ../../JuceLibraryCode/include_juce_javascript.cpp; sourceTree = SOURCE_ROOT; };
		F0C119A4900CDD4B906DE49D /* include_juce_gui_extra.mm */ /* include_juce_gui_extra.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_extra.mm; path = ../../JuceLibraryCode/include_juce_gui_extra.mm; sourceTree = SOURCE_ROOT; };
		F1204BECCB8F50EEE40B1146 /* LFO.h */ /* LFO.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = LFO.h; path = ../../Source/LFO.h; sourceTree = SOURCE_ROOT; };
		F1598BF2DF23C60E456F5318 /* include_juce_graphics.mm */ /* include_juce_graphics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_graphics.mm; path = ../../JuceLibraryCode/include_juce_graphics.mm; sourceTree = SOURCE_ROOT; };
		F29AEBE4391B30D09F4EE00C /* RecentFilesMenuTemplate.nib */ /* RecentFilesMenuTemplate.nib */ = {isa = PBXFileReference; lastKnownFileType = file.nib; name = RecentFilesMenuTemplate.nib; path = RecentFilesMenuTemplate.nib; sourceTree = SOURCE_ROOT; };
		F33F69952CD4E9FEE39F559C /* Foundation.framework */ /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		F5237872170576E3AEAA5610 /* MacroControlPanel.cpp */ /* MacroControlPanel.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = MacroControlPanel.cpp; path = ../../Source/MacroControlPanel.cpp; sourceTree = SOURCE_ROOT; };
		F6C35D2D472C33ACDC3E0C6D /* ParticleSystem.cpp */ /* ParticleSystem.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = ParticleSystem.cpp; path = ../../Source/ParticleSystem.cpp; sourceTree = SOURCE_ROOT; };
		F9D23F95622E2C8B86734CE7 /* VST3 */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AuraBloom.vst3; sourceTree = BUILT_PRODUCTS_DIR; };
		FDC00FFDBC9AD77B9BBCDC45 /* ModulationSourceComponent.h */ /* ModulationSourceComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ModulationSourceComponent.h; path = ../../Source/ModulationSourceComponent.h; sourceTree = SOURCE_ROOT; };
		FE6940B8CDE4BBC3C8E402E5 /* PluginEditor.cpp */ /* PluginEditor.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = PluginEditor.cpp; path = ../../Source/PluginEditor.cpp; sourceTree = SOURCE_ROOT; };
		FEB3B6925A0F5C2015A6AB49 /* juce_dsp */ /* juce_dsp */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_dsp; path = "~/JUCE/modules/juce_dsp"; sourceTree = "<absolute>"; };
		FECE2A1DB04AE4AC53907506 /* ModulationMatrixComponent.h */ /* ModulationMatrixComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ModulationMatrixComponent.h; path = ../../Source/ModulationMatrixComponent.h; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		3C081BC323134FC2945CA2F4 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5A7CD5921E4DA250C0016C91,
				856795A7F1A2F281288A0302,
				50A0C4E1F3B1A45D4B38153B,
				0BAFDF43895A9805CC767162,
				602DFFCE42CB2B9ADB17AA7D,
				BCC12C5F7EB4902E3DAAA77A,
				872A09E6955670ADD2CA0CE8,
				41A17867C6FFCF23FB1153F4,
				E486B33A167995190B79CE35,
				A4977406877BC634B8CBA122,
				E7A499297F940B0AC7142E5D,
				4426BE35E11C23D38A631D9E,
				62C626F79B9454A69BF0DEE3,
				D6E268842908E141D37699A6,
				06FA3F1AE5D96B0D0E3E4EB0,
				51826A6DCD661F39BE7B1DDE,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6B247D12BDF6A4E6B5DA5DBF = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5A7CD5921E4DA250C0016C91,
				856795A7F1A2F281288A0302,
				50A0C4E1F3B1A45D4B38153B,
				0BAFDF43895A9805CC767162,
				602DFFCE42CB2B9ADB17AA7D,
				BCC12C5F7EB4902E3DAAA77A,
				872A09E6955670ADD2CA0CE8,
				41A17867C6FFCF23FB1153F4,
				E486B33A167995190B79CE35,
				A4977406877BC634B8CBA122,
				E7A499297F940B0AC7142E5D,
				4426BE35E11C23D38A631D9E,
				62C626F79B9454A69BF0DEE3,
				D6E268842908E141D37699A6,
				06FA3F1AE5D96B0D0E3E4EB0,
				51826A6DCD661F39BE7B1DDE,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FEA7884CA3D7738ABD2352CC = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5A7CD5921E4DA250C0016C91,
				856795A7F1A2F281288A0302,
				50A0C4E1F3B1A45D4B38153B,
				0BAFDF43895A9805CC767162,
				602DFFCE42CB2B9ADB17AA7D,
				BCC12C5F7EB4902E3DAAA77A,
				872A09E6955670ADD2CA0CE8,
				41A17867C6FFCF23FB1153F4,
				E486B33A167995190B79CE35,
				A4977406877BC634B8CBA122,
				E7A499297F940B0AC7142E5D,
				4426BE35E11C23D38A631D9E,
				62C626F79B9454A69BF0DEE3,
				D6E268842908E141D37699A6,
				06FA3F1AE5D96B0D0E3E4EB0,
				51826A6DCD661F39BE7B1DDE,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1666768EB3A171C3DA3B0806 /* Source */ = {
			isa = PBXGroup;
			children = (
				D947DEF6F12E16B4A088AF80,
				9B9CCFC3DDD558D8DB5A529E,
				786FCF62D66A890C55885FC8,
				E5FDB5066E4D76DF5BA0B822,
				91F1A42CCAE0D5E7B5C286AE,
				7238216CE0AD2CFAE3F2F2AE,
			);
			name = Source;
			sourceTree = "<group>";
		};
		3C2FE9679C1E0E353C3E0E29 /* Source */ = {
			isa = PBXGroup;
			children = (
				E0973D65C324CEB1BAE442AA,
				427A4BC3A82CEA8CC1220E2E,
				FE6940B8CDE4BBC3C8E402E5,
				15A3750971D43E7F215E7F01,
				590FCE69D76373DD57C7735B,
				2EDA7298D8717F131196223A,
				42BC727F20E66A7E93E8FE0B,
				82E94BB9321B6079D33A8DEB,
				F6C35D2D472C33ACDC3E0C6D,
				CFA3941115B23C91DC4202DF,
				9F7231CD31733BC12553BA3C,
				584D94E13B152348B1BB191A,
				4D58C9173D7CCB406552BBC3,
				B47DA7C6CEDB7C61AB043760,
				99BC596407ED8C1440AD44B0,
				2D70D90EEE1215CCDC737F77,
				C71370DC400D31549CE99450,
				258DAA20F71E92EA208949B5,
				95F1CD71ABE1C6E9AAB36B66,
				21471F0C65A747972FB96C49,
				F1204BECCB8F50EEE40B1146,
				B89CADA2ED9D0916075D5B09,
				B9CA51E55256908CBAD0DA87,
				25661989A53E1D912513E1B2,
				92E49DC2D9FED8A0227A00BD,
				4D58FAEE9EB2C38E7E975C6E,
				C963796A02656D6B59D939FB,
				721FFAED6EBA9FF2E7E1BE9A,
				73832FFDF7BA7BF355C9CA05,
				C6EA8A059154CFEEC6B42A43,
				E406DDA831E57FE55D0A4C85,
				FDC00FFDBC9AD77B9BBCDC45,
				73EC4A329260AB4BA962551A,
				6D47F0797F8269D5E7225FFD,
				616AD6AF72738B5C6719C9A1,
				AA6ED7696921421C989B5D5D,
				A2253D442374D45BE7920BA3,
				FECE2A1DB04AE4AC53907506,
				4F2BE73A9E6F665BB9B58ED6,
				2B6F1C59C0894D84C4181F03,
				2D8CFA670B8A34F707CE2E0E,
				7BC6A9D4580448CFBF5B5E75,
				9048707A91911B226276677C,
				E45116766D28C46A3C57951A,
				F5237872170576E3AEAA5610,
				490C7CEE4945037D2D203EC7,
				411E413E8BCC15E90E1391D1,
			);
			name = Source;
			sourceTree = "<group>";
		};
		7238216CE0AD2CFAE3F2F2AE /* Products */ = {
			isa = PBXGroup;
			children = (
				F9D23F95622E2C8B86734CE7,
				C6CB0D2D5996E9826C4F7DD1,
				568CF448626993FEBB86509C,
				72B94E2C27EB2B1251881CCC,
			);
			name = Products;
			sourceTree = "<group>";
		};
		786FCF62D66A890C55885FC8 /* JUCE Library Code */ = {
			isa = PBXGroup;
			children = (
				E3E1E19863D2BB2F71482CBA,
				E3FFEB0501F74E3B1BE40E6D,
				C320DB515A51A03E4D8994C1,
				D303775627490EC796169B93,
				AD8B16248A561310D9A5A52B,
				2DA473EBC955467DA8AC961E,
				0637A923AEEA196D33E23530,
				E9AE260B5826495B625DE856,
				868AA0D02FFB870A50878526,
				4293B3F8D7AB3C4F60E5FE6D,
				908C3088365D80179B896543,
				6699363EB4CC7A1A13D8F24C,
				2452132BF82D51F543CC1724,
				4AF0810ED05BC74C38E6D906,
				08C83A456C9F45E4B25B5BE7,
				F1598BF2DF23C60E456F5318,
				D7F85B6432D7B8D4FDEC9B12,
				03449502B843566955E501A9,
				48A8D4F1FF41C31F8C24916B,
				F0C119A4900CDD4B906DE49D,
				EFC412C9AC94A2BCA03ABE5B,
				D2D67EEA2BC55C62A3072213,
				16570337C74E2E4739B8BA25,
				A12C6C155F85967C01DA3AC2,
				802576648FFFF7DF3E174990,
			);
			name = "JUCE Library Code";
			sourceTree = "<group>";
		};
		91F1A42CCAE0D5E7B5C286AE /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				BD02B2AA7C2A48AF1D8C8994,
				ED6D9A401C61F054BB1BD7C2,
				C9ED7BB29FD4340010B5FFAC,
				A8D5042BE73F978E6408081B,
				0E1AF2A5ACF4831C99DA75AA,
				36C7116EF4C20979C77C5D3E,
				CB2E64CB6A8E3456623888A9,
				60BB0B870CB93C92EDDF98D8,
				F33F69952CD4E9FEE39F559C,
				6FD8DB6D45F3DCFDCCB310E7,
				1234510402282F87776525ED,
				93469C8A2EB3B58DE3BC859E,
				404CE0BFAFECCA2C07A1CED0,
				0509A229851B05A89BE3BF9F,
				EEECF9BA18A6E81DB3BFABA4,
				EB4910129791947198A07E71,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9B9CCFC3DDD558D8DB5A529E /* JUCE Modules */ = {
			isa = PBXGroup;
			children = (
				DF9D003B8C99490B5C7B34F2,
				28E12ECDE889A211EDC30F14,
				0204C27C414D31A2BCA84E97,
				38D24ED25DDCB77B4358F0E6,
				3BCF7665954C2AA7EAB0B28E,
				DB87960214EE673533427D90,
				740FF311E2E695391B1165A0,
				D174D520AA11B8C7021488A8,
				FEB3B6925A0F5C2015A6AB49,
				0359B6C49F41ED487C08C746,
				9256A1F6A000F19E237E5F05,
				BD771DC007E46D25F73E233E,
				36B9C4C5F9454F323E816271,
				29B43323580FA73FB9ECBAEA,
				EE51CC922DAABBB24C5A933B,
				711283609CB8CBE78EA8A54B,
			);
			name = "JUCE Modules";
			sourceTree = "<group>";
		};
		D947DEF6F12E16B4A088AF80 /* AuraBloom */ = {
			isa = PBXGroup;
			children = (
				3C2FE9679C1E0E353C3E0E29,
			);
			name = AuraBloom;
			sourceTree = "<group>";
		};
		E5FDB5066E4D76DF5BA0B822 /* Resources */ = {
			isa = PBXGroup;
			children = (
				A1AEF826748837F4ABE7C23A,
				48F05E2EF772805C0A8C02AA,
				DB055DE38C17A7B0563E3A79,
				F29AEBE4391B30D09F4EE00C,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1264F2B74CCB773D335936AC /* AuraBloom - VST3 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6811BA6A1F59AF243F4ADDEF;
			buildPhases = (
				AE120290BB46DA17D0D6B2FC,
				73225B281573D2D6656A1061,
				6B247D12BDF6A4E6B5DA5DBF,
				9EB23DAF06D5FA86DD25C413,
				A2C936BAA7372F16F45BC62B,
			);
			buildRules = (
			);
			dependencies = (
				766AB31598DC1FE1EE0FF393,
				D743617DF96F21DDAD10F804,
			);
			name = "AuraBloom - VST3";
			productName = AuraBloom;
			productReference = F9D23F95622E2C8B86734CE7;
			productType = "com.apple.product-type.bundle";
		};
		222E364D8E1EC5C4A255D376 /* AuraBloom - Standalone Plugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BA1A88F00242BDCE7B43C641;
			buildPhases = (
				D961BE0EE30FDA33810440F0,
				4902C187EF65637A8AA8A833,
				FEA7884CA3D7738ABD2352CC,
			);
			buildRules = (
			);
			dependencies = (
				5430580151010D35D6E3B2F5,
			);
			name = "AuraBloom - Standalone Plugin";
			productName = AuraBloom;
			productReference = C6CB0D2D5996E9826C4F7DD1;
			productType = "com.apple.product-type.application";
		};
		7DC1688EB79D54D4818A022B /* AuraBloom - VST3 Manifest Helper */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8A5779FA4BF8F577B0B426BF;
			buildPhases = (
				6F87719BD71D8228912E69DE,
				3C081BC323134FC2945CA2F4,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "AuraBloom - VST3 Manifest Helper";
			productName = AuraBloom;
			productReference = 72B94E2C27EB2B1251881CCC;
			productType = "com.apple.product-type.tool";
		};
		B70BFF1C839CA3B743B4C761 /* AuraBloom - Shared Code */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9969910E263EBE55C92D87B4;
			buildPhases = (
				7ACB29AD44434CF2B3891429,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "AuraBloom - Shared Code";
			productName = AuraBloom;
			productReference = 568CF448626993FEBB86509C;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B8C891B837B7AC357B988433 = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1340;
				ORGANIZATIONNAME = "YourCompany";
				TargetAttributes = {
					1264F2B74CCB773D335936AC = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					222E364D8E1EC5C4A255D376 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					516691A5770BC0B6624ED333 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					7DC1688EB79D54D4818A022B = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					B70BFF1C839CA3B743B4C761 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
				};
			};
			buildConfigurationList = DA26A3ACC9C4E13B2677B16A;
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1666768EB3A171C3DA3B0806;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				516691A5770BC0B6624ED333,
				1264F2B74CCB773D335936AC,
				222E364D8E1EC5C4A255D376,
				B70BFF1C839CA3B743B4C761,
				7DC1688EB79D54D4818A022B,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AE120290BB46DA17D0D6B2FC = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				60F17317DE8027B281449AAC,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D961BE0EE30FDA33810440F0 = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				60F17317DE8027B281449AAC,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		9EB23DAF06D5FA86DD25C413 /* Update manifest */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			name = "Update manifest";
			alwaysOutOfDate = 1;
			shellPath = /bin/sh;
			shellScript = "set -euo pipefail\n\necho Running codesign --verbose=4 --force --sign - \\\"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\\\"\ncodesign --verbose=4 --force --sign - \"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\"\n\necho Running \\\"${CONFIGURATION_BUILD_DIR}/juce_vst3_helper\\\" -create -version \\\"1.0.0\\\" -path \\\"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\\\" -output \\\"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}/Contents/Resources/moduleinfo.json\\\"\n\"${CONFIGURATION_BUILD_DIR}/juce_vst3_helper\" -create -version \"1.0.0\" -path \"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}\" -output \"${CONFIGURATION_BUILD_DIR}/${WRAPPER_NAME}/Contents/Resources/moduleinfo.json\"\n";
		};
		A2C936BAA7372F16F45BC62B /* Plugin Copy Step */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			name = "Plugin Copy Step";
			alwaysOutOfDate = 1;
			shellPath = /bin/sh;
			shellScript = "set -euo pipefail\n\nif [[ \"${CONFIGURATION}\" == \"Debug\" ]]; then\n  destinationPlugin=\"${HOME}/Library/Audio/Plug-Ins/VST3//$(basename \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\")\"\n  echo Running rm -rf \\\"${destinationPlugin}\\\"\n  rm -rf \"${destinationPlugin}\"\n  echo Running ditto \\\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\\\" \\\"${destinationPlugin}\\\"\n  ditto \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\" \"${destinationPlugin}\"\n\n  if [[ -n \"${EXPANDED_CODE_SIGN_IDENTITY-}\" ]]; then\n    if [[ -n \"${CODE_SIGN_ENTITLEMENTS-}\" ]]; then\n      entitlementsArg=(--entitlements \"${CODE_SIGN_ENTITLEMENTS}\")\n    fi\n\n    echo Signing Identity: \\\"${EXPANDED_CODE_SIGN_IDENTITY_NAME}\\\"\n    echo Running codesign --verbose=4 --force --sign \\\"${EXPANDED_CODE_SIGN_IDENTITY}\\\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \\\"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\\\"\n    codesign --verbose=4 --force --sign \"${EXPANDED_CODE_SIGN_IDENTITY}\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\"\n  fi\nfi\n\nif [[ \"${CONFIGURATION}\" == \"Release\" ]]; then\n  destinationPlugin=\"${HOME}/Library/Audio/Plug-Ins/VST3//$(basename \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\")\"\n  echo Running rm -rf \\\"${destinationPlugin}\\\"\n  rm -rf \"${destinationPlugin}\"\n  echo Running ditto \\\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\\\" \\\"${destinationPlugin}\\\"\n  ditto \"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\" \"${destinationPlugin}\"\n\n  if [[ -n \"${EXPANDED_CODE_SIGN_IDENTITY-}\" ]]; then\n    if [[ -n \"${CODE_SIGN_ENTITLEMENTS-}\" ]]; then\n      entitlementsArg=(--entitlements \"${CODE_SIGN_ENTITLEMENTS}\")\n    fi\n\n    echo Signing Identity: \\\"${EXPANDED_CODE_SIGN_IDENTITY_NAME}\\\"\n    echo Running codesign --verbose=4 --force --sign \\\"${EXPANDED_CODE_SIGN_IDENTITY}\\\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \\\"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\\\"\n    codesign --verbose=4 --force --sign \"${EXPANDED_CODE_SIGN_IDENTITY}\" ${entitlementsArg[*]-} ${OTHER_CODE_SIGN_FLAGS-} \"${HOME}/Library/Audio/Plug-Ins/VST3//${WRAPPER_NAME}\"\n  fi\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4902C187EF65637A8AA8A833 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7049F4422D75E70238527780,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6F87719BD71D8228912E69DE = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				02884CA29281A751E889F69A,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		73225B281573D2D6656A1061 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DA5EFA4628FD7351CBB41503,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7ACB29AD44434CF2B3891429 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6FC8D356862CAAF459C332D8,
				2AF00C9F6C9F11E2ACE4E120,
				BB83188F7C0EE5765B8BAFDC,
				34C45D14B6EA3B3A0D11FB0E,
				085CB5CE77BC38E064C8A18E,
				6F88C6F8AAC07C6202D5C0B4,
				D6EE4C4446507BE27B9D01C0,
				FA8B5697AE23312A68F36B75,
				6F1962120F68FF814CC306D8,
				C77DA9DC2B155D6BF55A48BC,
				3131EF3454491B2D04948E79,
				31266DEFF4BD3937F72A2F32,
				26F0F79FF12A1988CB5773D1,
				47B0D46DE5693EA5C1608216,
				2B6C6BCBA54EA61E9BADE946,
				1D7663C8A2B72D7CEDA50E85,
				13E479489711E992EE5939A1,
				FC75F3F4A7DC40B9E74DBF8B,
				34F159090ED4818C0B97DE48,
				3AC927120D778BD7D6232ABE,
				C7ADDCE7967C3832971BEE3A,
				3A6C6B235B18D311994630E5,
				E86EAEFBA84B15CF05713FC9,
				E393D94E4209642AAB121BA7,
				B5884CAEF2FA168823493E36,
				7FF13CD4EA063994674CE3CB,
				CC34E7B039386D8B715C2063,
				30760C49A1625FAD15D61651,
				5FA71FD9ADB72C1DBFFCCEE0,
				8B9CE2AC3A68EB6CFE8FEFE5,
				80F8C61EF1EEFDF8ECDA7B84,
				5554EFFFEDF827FF7F2B938D,
				B6A41C00F9FD5798FC06EE0C,
				665D700DB3EDBFD328C28C25,
				5A157DC93AD3AB071B068FD7,
				7981AE50F22167766049349F,
				81369D5288DEFAE5CF4E24CD,
				8740BA28BB317D88BB619F03,
				281C7AA2CA9B4D81823AE022,
				9BCA378A38B53E26430782DF,
				12B2E10A73C69A80541602D0,
				0988A3D48CA9D594F078311C,
				7D0C6B9467221555C20D9046,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		3A30F4D903C189B3AA332064 = {
			isa = PBXTargetDependency;
			target = B70BFF1C839CA3B743B4C761;
		};
		5430580151010D35D6E3B2F5 = {
			isa = PBXTargetDependency;
			target = B70BFF1C839CA3B743B4C761;
		};
		766AB31598DC1FE1EE0FF393 = {
			isa = PBXTargetDependency;
			target = B70BFF1C839CA3B743B4C761;
		};
		9A89C814310C0C215F5B5C49 = {
			isa = PBXTargetDependency;
			target = 1264F2B74CCB773D335936AC;
		};
		C90ECAC542EC0B9EC3C0EE02 = {
			isa = PBXTargetDependency;
			target = 222E364D8E1EC5C4A255D376;
		};
		D743617DF96F21DDAD10F804 = {
			isa = PBXTargetDependency;
			target = 7DC1688EB79D54D4818A022B;
		};
		EF555A1F43FDD65ECC3904F9 = {
			isa = PBXTargetDependency;
			target = 7DC1688EB79D54D4818A022B;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		30002FA4744E060EAD4C921A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				DEPLOYMENT_POSTPROCESSING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = "NO";
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3_Manifest_Helper.plist;
				INFOPLIST_PREPROCESS = NO;
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "juce_vst3_helper";
				SEPARATE_STRIP = YES;
				STRIPFLAGS = "-x";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		375D2252C81CC0313A0042CE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				DEPLOYMENT_POSTPROCESSING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = "NO";
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-Standalone_Plugin.plist;
				INFOPLIST_PREPROCESS = NO;
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-lAuraBloom";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "AuraBloom";
				SEPARATE_STRIP = YES;
				STRIPFLAGS = "-x";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		39077ACEC2A17277E5280F4E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				DEPLOYMENT_POSTPROCESSING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = "NO";
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GENERATE_PKGINFO_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Library/Audio/Plug-Ins/VST3/";
				LIBRARY_STYLE = Bundle;
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-bundle -lAuraBloom";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "AuraBloom";
				SEPARATE_STRIP = YES;
				STRIPFLAGS = "-x";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
				WRAPPER_EXTENSION = vst3;
			};
			name = Release;
		};
		43D3ED208E18D74E84A312AF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = "NO";
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JUCE_SHARED_CODE=1",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INSTALL_PATH = "@executable_path/../Frameworks";
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "AuraBloom";
				SKIP_INSTALL = YES;
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		70BA0DEBBED99BD184CEA1C9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "AuraBloom";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Release;
		};
		759C8D12B8AE8A768C9BC075 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				DEPLOYMENT_POSTPROCESSING = YES;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = "YES;DEBUG_INFORMATION_FORMAT=dwarf-with-dsym";
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1;JUCE_DEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3_Manifest_Helper.plist;
				INFOPLIST_PREPROCESS = NO;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "juce_vst3_helper";
				SEPARATE_STRIP = YES;
				STRIPFLAGS = "-x";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
		8AB1983A70F8C62E86DAE02B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				SDKROOT = macosx;
			};
			name = Release;
		};
		B2E49FF37CF1A04E479924CA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = "YES;DEBUG_INFORMATION_FORMAT=dwarf-with-dsym";
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1;JUCE_DEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JUCE_SHARED_CODE=1",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INSTALL_PATH = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "AuraBloom";
				SKIP_INSTALL = YES;
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
		B4A84DE5B034791332316D95 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				DEPLOYMENT_POSTPROCESSING = YES;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = "YES;DEBUG_INFORMATION_FORMAT=dwarf-with-dsym";
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1;JUCE_DEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=1",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-Standalone_Plugin.plist;
				INFOPLIST_PREPROCESS = NO;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-lAuraBloom";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "AuraBloom";
				SEPARATE_STRIP = YES;
				STRIPFLAGS = "-x";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
		C06C0C081A3F71D4A6ECC184 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				DEPLOYMENT_POSTPROCESSING = YES;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = "YES;DEBUG_INFORMATION_FORMAT=dwarf-with-dsym";
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1;JUCE_DEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_VST3_CAN_REPLACE_VST2=0",
					"JUCE_PLUGINHOST_VST=0",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_AU=1",
					"JUCE_PLUGINHOST_LADSPA=0",
					"JUCE_PLUGINHOST_LV2=0",
					"JUCE_USE_CURL=0",
					"JUCE_LOAD_CURL_SYMBOLS_LAZILY=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_ENABLE_REPAINT_DEBUGGING=0",
					"JUCE_USE_XRANDR=1",
					"JUCE_USE_XINERAMA=1",
					"JUCE_USE_XSHM=1",
					"JUCE_USE_XRENDER=0",
					"JUCE_USE_XCURSOR=1",
					"JUCE_WEB_BROWSER=0",
					"JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=1",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
					"JucePlugin_Enable_IAA=0",
					"JucePlugin_Enable_ARA=0",
					"JucePlugin_Name=\\\"AuraBloom\\\"",
					"JucePlugin_Desc=\\\"Granular\\ synthesis\\ plugin\\ with\\ particle\\ effects\\\"",
					"JucePlugin_Manufacturer=\\\"YourCompany\\\"",
					"JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"",
					"JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"",
					"JucePlugin_ManufacturerCode=0x5972636d",
					"JucePlugin_PluginCode=0x41757262",
					"JucePlugin_IsSynth=0",
					"JucePlugin_WantsMidiInput=0",
					"JucePlugin_ProducesMidiOutput=0",
					"JucePlugin_IsMidiEffect=0",
					"JucePlugin_EditorRequiresKeyboardFocus=0",
					"JucePlugin_Version=1.0.0",
					"JucePlugin_VersionCode=0x10000",
					"JucePlugin_VersionString=\\\"1.0.0\\\"",
					"JucePlugin_VSTUniqueID=JucePlugin_PluginCode",
					"JucePlugin_VSTCategory=kPlugCategEffect",
					"JucePlugin_Vst3Category=\\\"Fx\\\"",
					"JucePlugin_AUMainType=\\'aufx\\'",
					"JucePlugin_AUSubType=JucePlugin_PluginCode",
					"JucePlugin_AUExportPrefix=AuraBloomAU",
					"JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"",
					"JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom",
					"JucePlugin_AAXIdentifier=com.yourcompany.aurabloom",
					"JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode",
					"JucePlugin_AAXProductId=JucePlugin_PluginCode",
					"JucePlugin_AAXCategory=2",
					"JucePlugin_AAXDisableBypass=0",
					"JucePlugin_AAXDisableMultiMono=0",
					"JucePlugin_IAAType=0x61757278",
					"JucePlugin_IAASubType=JucePlugin_PluginCode",
					"JucePlugin_IAAName=\\\"YourCompany:\\ AuraBloom\\\"",
					"JucePlugin_VSTNumMidiInputs=16",
					"JucePlugin_VSTNumMidiOutputs=16",
					"JucePlugin_ARAContentTypes=0",
					"JucePlugin_ARATransformationFlags=0",
					"JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"",
					"JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"",
					"JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"",
					"JucePlugin_MaxNumInputChannels=2",
					"JucePlugin_MaxNumOutputChannels=2",
					"JucePlugin_PreferredChannelConfigurations={1,\\ 1},\\ {2,\\ 2}",
					"JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GENERATE_PKGINFO_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(HOME)/JUCE/modules",
					"$(HOME)/JUCE/modules/juce_audio_plugin_client/AU",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-VST3.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Library/Audio/Plug-Ins/VST3/";
				LIBRARY_STYLE = Bundle;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(HOME)/JUCE/modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(HOME)/JUCE/modules $(HOME)/JUCE/modules/juce_audio_plugin_client/AU";
				OTHER_LDFLAGS = "-bundle -lAuraBloom";
				PRODUCT_BUNDLE_IDENTIFIER = com.YourCompany.AuraBloom;
				PRODUCT_NAME = "AuraBloom";
				SEPARATE_STRIP = YES;
				STRIPFLAGS = "-x";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
				WRAPPER_EXTENSION = vst3;
			};
			name = Debug;
		};
		E3AD8D5F7EB371D4B34ED73F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "AuraBloom";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Debug;
		};
		F26550A2F9C8E5EBEA7A595E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				SDKROOT = macosx;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6811BA6A1F59AF243F4ADDEF = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C06C0C081A3F71D4A6ECC184,
				39077ACEC2A17277E5280F4E,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		8A5779FA4BF8F577B0B426BF = {
			isa = XCConfigurationList;
			buildConfigurations = (
				759C8D12B8AE8A768C9BC075,
				30002FA4744E060EAD4C921A,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		9969910E263EBE55C92D87B4 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B2E49FF37CF1A04E479924CA,
				43D3ED208E18D74E84A312AF,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		BA1A88F00242BDCE7B43C641 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B4A84DE5B034791332316D95,
				375D2252C81CC0313A0042CE,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		DA26A3ACC9C4E13B2677B16A = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70BA0DEBBED99BD184CEA1C9,
				E3AD8D5F7EB371D4B34ED73F,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		E28518ED7E5BEE969BBE425B = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F26550A2F9C8E5EBEA7A595E,
				8AB1983A70F8C62E86DAE02B,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = B8C891B837B7AC357B988433 /* Project object */;
}

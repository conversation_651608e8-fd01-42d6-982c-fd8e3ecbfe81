/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin processor.

  ==============================================================================
*/

#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
AuraBloomAudioProcessor::AuraBloomAudioProcessor()
#ifndef JucePlugin_PreferredChannelConfigurations
     : AudioProcessor (BusesProperties()
                     #if ! JucePlugin_IsMidiEffect
                      #if ! JucePlugin_IsSynth
                       .withInput  ("Input",  juce::AudioChannelSet::stereo(), true)
                      #endif
                       .withOutput ("Output", juce::AudioChannelSet::stereo(), true)
                     #endif
                       ),
      parameters (*this, nullptr, "AuraBloom", AuraBloomParameters::createParameterLayout()),
#else
     : parameters (*this, nullptr, "AuraBloom", AuraBloomParameters::createParameterLayout()),
#endif
      particleSystem(),
      emitter(),
      audioBufferManager(),
      grainGenerator(),
      modulationMatrix()
{
    DBG_LOG("Constructor started");

    try {
        // Initialize particle system
        particleSystem.initialize(1000, 44100.0, 512);
        DBG_LOG("Particle system initialized");

        // Initialize emitter
        emitter.initialize(&particleSystem);
        DBG_LOG("Emitter initialized");

        // Set up parameter listeners
        updateParameters();
        DBG_LOG("Parameters updated");

        // Initialize audio effects
        initializeAudioEffects();
        DBG_LOG("Audio effects initialized");
    }
    catch (const std::exception& e) {
        DBG_LOG("Exception in constructor: " + juce::String(e.what()));
    }
    catch (...) {
        DBG_LOG("Unknown exception in constructor");
    }

    DBG_LOG("Constructor completed");
}

AuraBloomAudioProcessor::~AuraBloomAudioProcessor()
{
    DBG_LOG("Destructor started");

    try {
        // Clear any active grains to prevent access to invalid memory
        grainGenerator.clearAllGrains();
        DBG_LOG("Grains cleared");

        // Clear particle system
        particleSystem.clear();
        DBG_LOG("Particle system cleared");

        // Clear audio buffer
        audioBufferManager.clear();
        DBG_LOG("Audio buffer cleared");
    }
    catch (const std::exception& e) {
        DBG_LOG("Exception in destructor: " + juce::String(e.what()));
    }
    catch (...) {
        DBG_LOG("Unknown exception in destructor");
    }

    DBG_LOG("Destructor completed");
}

//==============================================================================
const juce::String AuraBloomAudioProcessor::getName() const
{
    return JucePlugin_Name;
}

bool AuraBloomAudioProcessor::acceptsMidi() const
{
   #if JucePlugin_WantsMidiInput
    return true;
   #else
    return false;
   #endif
}

bool AuraBloomAudioProcessor::producesMidi() const
{
   #if JucePlugin_ProducesMidiOutput
    return true;
   #else
    return false;
   #endif
}

bool AuraBloomAudioProcessor::isMidiEffect() const
{
   #if JucePlugin_IsMidiEffect
    return true;
   #else
    return false;
   #endif
}

double AuraBloomAudioProcessor::getTailLengthSeconds() const
{
    return 0.0;
}

int AuraBloomAudioProcessor::getNumPrograms()
{
    return 1;   // NB: some hosts don't cope very well if you tell them there are 0 programs,
                // so this should be at least 1, even if you're not really implementing programs.
}

int AuraBloomAudioProcessor::getCurrentProgram()
{
    return 0;
}

void AuraBloomAudioProcessor::setCurrentProgram (int index)
{
}

const juce::String AuraBloomAudioProcessor::getProgramName (int index)
{
    return {};
}

void AuraBloomAudioProcessor::changeProgramName (int index, const juce::String& newName)
{
}

//==============================================================================
void AuraBloomAudioProcessor::prepareToPlay (double sampleRate, int samplesPerBlock)
{
    // Initialize particle system with current sample rate and block size
    particleSystem.initialize(1000, sampleRate, samplesPerBlock);

    // Initialize input buffer
    inputBuffer.setSize(getTotalNumInputChannels(), samplesPerBlock * 10); // Buffer for 10 blocks
    inputBuffer.clear();

    // Initialize audio buffer manager
    audioBufferManager.initialize(getTotalNumInputChannels(), 5.0f, sampleRate); // 5 seconds buffer

    // Initialize grain generator
    grainGenerator.initialize(1000, sampleRate); // 1000 grains max
    grainGenerator.setDensity(static_cast<float>(*parameters.getRawParameterValue(AuraBloomParameters::GRAIN_DENSITY_ID)));

    // Initialize modulation matrix
    initializeModulationMatrix();

    // Initialize DSP spec for audio effects
    spec.sampleRate = sampleRate;
    spec.maximumBlockSize = static_cast<juce::uint32>(samplesPerBlock);
    spec.numChannels = static_cast<juce::uint32>(getTotalNumOutputChannels());

    // Prepare audio effects
    lowPassFilter.prepare(spec);
    highPassFilter.prepare(spec);
    bandPassFilter.prepare(spec);
    notchFilter.prepare(spec);
    delayLine.prepare(spec);
    reverb.prepare(spec);

    // Set initial delay line size (1 second max)
    delayLine.setMaximumDelayInSamples(static_cast<int>(sampleRate));
}

void AuraBloomAudioProcessor::releaseResources()
{
    // When playback stops, you can use this as an opportunity to free up any
    // spare memory, etc.
}

#ifndef JucePlugin_PreferredChannelConfigurations
bool AuraBloomAudioProcessor::isBusesLayoutSupported (const BusesLayout& layouts) const
{
  #if JucePlugin_IsMidiEffect
    juce::ignoreUnused (layouts);
    return true;
  #else
    // This is the place where you check if the layout is supported.
    // In this template code we only support mono or stereo.
    // Some plugin hosts, such as certain GarageBand versions, will only
    // load plugins that support stereo bus layouts.
    if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::mono()
     && layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo())
        return false;

    // This checks if the input layout matches the output layout
   #if ! JucePlugin_IsSynth
    if (layouts.getMainOutputChannelSet() != layouts.getMainInputChannelSet())
        return false;
   #endif

    return true;
  #endif
}
#endif

void AuraBloomAudioProcessor::processBlock (juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    juce::ScopedNoDenormals noDenormals;
    auto totalNumInputChannels  = getTotalNumInputChannels();
    auto totalNumOutputChannels = getTotalNumOutputChannels();
    float deltaTime = buffer.getNumSamples() / static_cast<float>(getSampleRate());

    // Add safety checks
    if (buffer.getNumSamples() <= 0 || totalNumOutputChannels <= 0)
    {
        DBG_LOG("Invalid buffer parameters in processBlock");
        return;
    }

    try {

    // Clear any output channels that don't have input channels
    for (auto i = totalNumInputChannels; i < totalNumOutputChannels; ++i)
        buffer.clear (i, 0, buffer.getNumSamples());

    // Store input buffer for granular processing
    for (int channel = 0; channel < totalNumInputChannels; ++channel)
    {
        // Shift existing samples to make room for new ones
        for (int i = 0; i < inputBuffer.getNumSamples() - buffer.getNumSamples(); ++i)
        {
            inputBuffer.setSample(channel, i, inputBuffer.getSample(channel, i + buffer.getNumSamples()));
        }

        // Copy new samples to the end of the buffer
        for (int i = 0; i < buffer.getNumSamples(); ++i)
        {
            inputBuffer.setSample(channel, inputBuffer.getNumSamples() - buffer.getNumSamples() + i,
                                 buffer.getSample(channel, i));
        }
    }

    // Add audio data to buffer manager
    audioBufferManager.addAudioData(buffer);

    // CRITICAL FIX: Update parameters from UI controls
    updateParameters();

    // Update buffer manager playback position
    audioBufferManager.updatePlaybackPosition(deltaTime);

    // Update emitter
    emitter.update(deltaTime);

    // Update particle system
    particleSystem.update(deltaTime);

    // Map particle system to grain generator
    mapParticleSystemToGrainGenerator();

    // Update grain generator
    grainGenerator.update(deltaTime, audioBufferManager);

    // Update modulation sources
    modulationMatrix.updateSources(deltaTime);

    // Apply modulation to parameters
    modulationMatrix.applyModulation();

    // Create a copy of the input for dry signal
    juce::AudioBuffer<float> dryBuffer;
    dryBuffer.makeCopyOf(buffer);

    // Clear output buffer for wet signal
    buffer.clear();

    // Process audio through grain generator
    grainGenerator.processAudio(buffer, audioBufferManager);

    // Apply global gain with reasonable limiting
    float rawGain = static_cast<float>(*parameters.getRawParameterValue(AuraBloomParameters::GAIN_ID));
    float safeGain = juce::jlimit(-40.0f, 20.0f, rawGain); // 更合理的增益范围
    float gainValue = juce::Decibels::decibelsToGain(safeGain);

    // Apply wet/dry mix
    float mixValue = static_cast<float>(*parameters.getRawParameterValue(AuraBloomParameters::MIX_ID)) / 100.0f;

    // Apply gain to wet signal
    float finalGain = gainValue; // 移除过度的安全系数

    // 监控音频电平，只在真正过载时调整
    float maxLevel = 0.0f;
    for (int channel = 0; channel < totalNumOutputChannels; ++channel)
    {
        float channelMax = buffer.getMagnitude(channel, 0, buffer.getNumSamples());
        maxLevel = juce::jmax(maxLevel, channelMax);
    }

    // 只在真正过载时才降低增益
    if (maxLevel > 0.95f)
    {
        finalGain *= (0.9f / maxLevel); // 轻微压缩，保持音量
    }
    for (int channel = 0; channel < totalNumOutputChannels; ++channel)
    {
        buffer.applyGain(channel, 0, buffer.getNumSamples(), finalGain);
    }

    // Mix wet and dry signals
    for (int channel = 0; channel < totalNumOutputChannels; ++channel)
    {
        float* wetData = buffer.getWritePointer(channel);
        const float* dryData = dryBuffer.getReadPointer(channel);

        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            // Mix wet and dry
            wetData[sample] = wetData[sample] * mixValue + dryData[sample] * (1.0f - mixValue);
        }
    }

    // Update and apply audio effects
    updateAudioEffects();
    processAudioEffects(buffer);

    // Apply minimal soft limiting only when necessary
    for (int channel = 0; channel < totalNumOutputChannels; ++channel)
    {
        float* channelData = buffer.getWritePointer(channel);

        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            float inputSample = channelData[sample];

            // Only process when really close to clipping
            if (std::abs(inputSample) > 0.98f)
            {
                // Very gentle soft limiting
                channelData[sample] = std::tanh(inputSample * 0.9f);
            }

            // Final hard limiting to prevent actual clipping
            channelData[sample] = juce::jlimit(-0.99f, 0.99f, channelData[sample]);
        }
    }

    } // end try block
    catch (const std::exception& e) {
        DBG_LOG("Exception in processBlock: " + juce::String(e.what()));
        // Clear output buffer to prevent noise
        buffer.clear();
    }
    catch (...) {
        DBG_LOG("Unknown exception in processBlock");
        // Clear output buffer to prevent noise
        buffer.clear();
    }
}

//==============================================================================
bool AuraBloomAudioProcessor::hasEditor() const
{
    return true; // (change this to false if you choose to not supply an editor)
}

juce::AudioProcessorEditor* AuraBloomAudioProcessor::createEditor()
{
    try {
        DBG_LOG("Creating editor...");
        auto* editor = new AuraBloomAudioProcessorEditor (*this);
        DBG_LOG("Editor created successfully");
        return editor;
    }
    catch (const std::exception& e) {
        DBG_LOG("Exception creating editor: " + juce::String(e.what()));
        return nullptr;
    }
    catch (...) {
        DBG_LOG("Unknown exception creating editor");
        return nullptr;
    }
}

//==============================================================================
void AuraBloomAudioProcessor::getStateInformation (juce::MemoryBlock& destData)
{
    // Save parameters
    auto state = parameters.copyState();
    std::unique_ptr<juce::XmlElement> xml (state.createXml());
    copyXmlToBinary (*xml, destData);
}

void AuraBloomAudioProcessor::setStateInformation (const void* data, int sizeInBytes)
{
    // Restore parameters
    std::unique_ptr<juce::XmlElement> xmlState (getXmlFromBinary (data, sizeInBytes));

    if (xmlState.get() != nullptr)
        if (xmlState->hasTagName (parameters.state.getType()))
            parameters.replaceState (juce::ValueTree::fromXml (*xmlState));
}

//==============================================================================
// This creates new instances of the plugin..
juce::AudioProcessor* JUCE_CALLTYPE createPluginFilter()
{
    return new AuraBloomAudioProcessor();
}

void AuraBloomAudioProcessor::updateParameters()
{
    // Set up emitter parameters with safety limits
    float rawRate = *parameters.getRawParameterValue(AuraBloomParameters::EMITTER_RATE_ID);
    float safeRate = juce::jlimit(0.1f, 100.0f, rawRate); // FIXED: Allow full Rate range
    emitter.setRate(safeRate);
    emitter.setBurstMode(*parameters.getRawParameterValue(AuraBloomParameters::EMITTER_BURST_MODE_ID) > 0.5f);
    emitter.setInitialVelocity(*parameters.getRawParameterValue(AuraBloomParameters::EMITTER_INITIAL_VELOCITY_ID));
    emitter.setSpread(*parameters.getRawParameterValue(AuraBloomParameters::EMITTER_SPREAD_ID));

    // Set emitter shape
    int shapeIndex = static_cast<int>(*parameters.getRawParameterValue(AuraBloomParameters::EMITTER_SHAPE_ID));
    emitter.setShape(static_cast<Emitter::Shape>(shapeIndex));

    // Set buffer parameters with safety limits
    emitter.setBufferPlaybackPosition(*parameters.getRawParameterValue(AuraBloomParameters::BUFFER_PLAYBACK_POSITION_ID));

    float rawScanSpeed = *parameters.getRawParameterValue(AuraBloomParameters::BUFFER_SCAN_SPEED_ID);
    float safeScanSpeed = juce::jlimit(-50.0f, 50.0f, rawScanSpeed); // Limit scan speed to ±50
    emitter.setBufferScanSpeed(safeScanSpeed);

    // Set buffer loop mode
    int loopModeIndex = static_cast<int>(*parameters.getRawParameterValue(AuraBloomParameters::BUFFER_LOOP_MODE_ID));
    emitter.setBufferLoopMode(static_cast<Emitter::LoopMode>(loopModeIndex));

    // Set particle system parameters
    particleSystem.setBehaviorIntensity(*parameters.getRawParameterValue(AuraBloomParameters::PARTICLE_BEHAVIOR_INTENSITY_ID) / 100.0f);
    particleSystem.setInteractionRadius(*parameters.getRawParameterValue(AuraBloomParameters::PARTICLE_INTERACTION_RADIUS_ID));
    particleSystem.setDamping(*parameters.getRawParameterValue(AuraBloomParameters::PARTICLE_DAMPING_ID) / 100.0f);

    // Set behavior mode
    int behaviorModeIndex = static_cast<int>(*parameters.getRawParameterValue(AuraBloomParameters::PARTICLE_BEHAVIOR_MODE_ID));
    particleSystem.setBehaviorMode(static_cast<ParticleSystem::BehaviorMode>(behaviorModeIndex));

    // Set audio buffer manager parameters with safety limits
    audioBufferManager.setPlaybackPosition(*parameters.getRawParameterValue(AuraBloomParameters::BUFFER_PLAYBACK_POSITION_ID) / 100.0f);
    audioBufferManager.setScanSpeed(safeScanSpeed); // Use same safe scan speed
    audioBufferManager.setLoopMode(loopModeIndex);

    // Set grain generator parameters with safety limits
    float rawDensity = *parameters.getRawParameterValue(AuraBloomParameters::GRAIN_DENSITY_ID);
    float safeDensity = juce::jlimit(0.1f, 15.0f, rawDensity); // Strictly limit density
    grainGenerator.setDensity(safeDensity);
}

void AuraBloomAudioProcessor::mapParticleSystemToGrainGenerator()
{
    // Get all active particles
    const auto& particles = particleSystem.getParticles();

    // Count active particles to adjust grain trigger probability
    int activeParticleCount = 0;
    for (const auto& particle : particles)
    {
        if (particle.isActive)
            activeParticleCount++;
    }

    // FIXED: Simplify particle to grain mapping, ensure stable continuous sound
    float baseProbability = 0.15f; // Increase base probability to ensure continuity

    // Simplified per-frame limit, avoid complex dynamic adjustments
    int maxGrainsPerFrame = juce::jlimit(5, 12, activeParticleCount / 10); // Simple mapping based on active particle count
    int grainsTriggeredThisFrame = 0;

    // For each active particle, potentially trigger a grain
    for (const auto& particle : particles)
    {
        if (particle.isActive && grainsTriggeredThisFrame < maxGrainsPerFrame)
        {
            // FIXED: Simplify trigger logic, ensure more stable grain generation
            if (juce::Random::getSystemRandom().nextFloat() < baseProbability)
            {
                // Map particle properties to grain parameters with safety bounds

                // Position: Map particle's x position to buffer position with clamping
                float normalizedPosition = juce::jlimit(0.0f, 1.0f, juce::jmap(particle.x, -100.0f, 100.0f, 0.0f, 1.0f));

                // Duration: Improved grain length mapping (100ms to 300ms) - longer grains create continuous texture
                float duration = juce::jlimit(0.1f, 0.3f, juce::jmap(particle.lifespan, 0.1f, 10.0f, 0.1f, 0.3f));

                // Pitch: More conservative pitch mapping - avoid extreme pitch shifts
                float pitch = juce::jlimit(0.9f, 1.1f, juce::jmap(particle.vy, -50.0f, 50.0f, 0.9f, 1.1f));

                // Pan: Narrower pan range - avoid extreme panning
                float pan = juce::jlimit(-0.3f, 0.3f, juce::jmap(particle.x, -100.0f, 100.0f, -0.3f, 0.3f));

                // Play direction: Based on particle's x velocity
                int playDirection = particle.vx > 0.0f ? 1 : -1;

                // Envelope type: Mainly use Gaussian window (0) for smoothest audio
                int envelopeType = juce::Random::getSystemRandom().nextFloat() < 0.9f ? 0 : 3; // 90% Gaussian, 10% cosine

                // Trigger the grain
                bool triggered = grainGenerator.triggerGrainNormalized(normalizedPosition, duration, pitch, pan, playDirection, envelopeType);

                // Count successful grain triggers
                if (triggered)
                    grainsTriggeredThisFrame++;
            }
        }
    }
}

void AuraBloomAudioProcessor::initializeModulationMatrix()
{
    // Create parameter targets for all parameters
    modulationMatrix.createParameterTargets(parameters);

    // Create modulation sources

    // LFO 1 - Rate modulation
    auto* lfo1 = new LFO("LFO 1");
    lfo1->setFrequency(1.0f);
    lfo1->setWaveformType(LFO::Sine);
    modulationMatrix.addSource(lfo1);

    // LFO 2 - Grain size modulation
    auto* lfo2 = new LFO("LFO 2");
    lfo2->setFrequency(0.5f);
    lfo2->setWaveformType(LFO::Triangle);
    modulationMatrix.addSource(lfo2);

    // Envelope 1 - Grain density modulation
    auto* env1 = new Envelope("Envelope 1");
    env1->setAttackTime(0.5f);
    env1->setDecayTime(1.0f);
    env1->setSustainLevel(0.7f);
    env1->setReleaseTime(2.0f);
    env1->setTriggerMode(Envelope::Loop);
    modulationMatrix.addSource(env1);

    // Create connections

    // LFO 1 -> Emitter Rate
    auto* rateTarget = modulationMatrix.getParameterTarget(AuraBloomParameters::EMITTER_RATE_ID);
    if (rateTarget != nullptr)
    {
        modulationMatrix.createConnection(lfo1, rateTarget, 0.3f);
    }

    // LFO 2 -> Grain Size
    auto* grainSizeTarget = modulationMatrix.getParameterTarget(AuraBloomParameters::GRAIN_SIZE_ID);
    if (grainSizeTarget != nullptr)
    {
        modulationMatrix.createConnection(lfo2, grainSizeTarget, 0.5f);
    }

    // Envelope 1 -> Grain Density
    auto* grainDensityTarget = modulationMatrix.getParameterTarget(AuraBloomParameters::GRAIN_DENSITY_ID);
    if (grainDensityTarget != nullptr)
    {
        modulationMatrix.createConnection(env1, grainDensityTarget, 0.4f);
    }

    // Trigger the envelope
    env1->noteOn();
}

void AuraBloomAudioProcessor::initializeAudioEffects()
{
    // Initialize filter coefficients
    currentFilterType = 0; // Low pass by default
    currentCutoff = 1000.0f;
    currentResonance = 1.0f;

    // Set initial filter coefficients (will be updated in updateAudioEffects)
    *lowPassFilter.state = *juce::dsp::IIR::Coefficients<float>::makeLowPass(44100.0, 1000.0f, 1.0f);
    *highPassFilter.state = *juce::dsp::IIR::Coefficients<float>::makeHighPass(44100.0, 1000.0f, 1.0f);
    *bandPassFilter.state = *juce::dsp::IIR::Coefficients<float>::makeBandPass(44100.0, 1000.0f, 1.0f);
    *notchFilter.state = *juce::dsp::IIR::Coefficients<float>::makeNotch(44100.0, 1000.0f, 1.0f);

    // Initialize reverb parameters
    juce::dsp::Reverb::Parameters reverbParams;
    reverbParams.roomSize = 0.5f;
    reverbParams.damping = 0.5f;
    reverbParams.wetLevel = 0.3f;
    reverbParams.dryLevel = 0.7f;
    reverbParams.width = 1.0f;
    reverbParams.freezeMode = 0.0f;
    reverb.setParameters(reverbParams);
}

void AuraBloomAudioProcessor::updateAudioEffects()
{
    // Get current parameter values
    int filterType = static_cast<int>(*parameters.getRawParameterValue(AuraBloomParameters::FILTER_TYPE_ID));
    float cutoff = *parameters.getRawParameterValue(AuraBloomParameters::FILTER_CUTOFF_ID);
    float resonance = *parameters.getRawParameterValue(AuraBloomParameters::FILTER_RESONANCE_ID);

    // Update filter if parameters changed
    if (filterType != currentFilterType || cutoff != currentCutoff || resonance != currentResonance)
    {
        currentFilterType = filterType;
        currentCutoff = cutoff;
        currentResonance = resonance;

        double sampleRate = getSampleRate();

        // Update filter coefficients
        *lowPassFilter.state = *juce::dsp::IIR::Coefficients<float>::makeLowPass(sampleRate, cutoff, resonance);
        *highPassFilter.state = *juce::dsp::IIR::Coefficients<float>::makeHighPass(sampleRate, cutoff, resonance);
        *bandPassFilter.state = *juce::dsp::IIR::Coefficients<float>::makeBandPass(sampleRate, cutoff, resonance);
        *notchFilter.state = *juce::dsp::IIR::Coefficients<float>::makeNotch(sampleRate, cutoff, resonance);
    }

    // Update delay time
    float delayTime = *parameters.getRawParameterValue(AuraBloomParameters::DELAY_TIME_ID);
    delayLine.setDelay(delayTime * getSampleRate() / 1000.0f); // Convert ms to samples

    // Update reverb size
    float reverbSize = *parameters.getRawParameterValue(AuraBloomParameters::REVERB_SIZE_ID);
    juce::dsp::Reverb::Parameters reverbParams = reverb.getParameters();
    reverbParams.roomSize = reverbSize / 100.0f; // Convert 0-100 to 0-1
    reverb.setParameters(reverbParams);
}

void AuraBloomAudioProcessor::processAudioEffects(juce::AudioBuffer<float>& buffer)
{
    // Create audio block for DSP processing
    juce::dsp::AudioBlock<float> block(buffer);
    juce::dsp::ProcessContextReplacing<float> context(block);

    // Apply filter based on current type
    switch (currentFilterType)
    {
        case 0: // Low Pass
            lowPassFilter.process(context);
            break;
        case 1: // High Pass
            highPassFilter.process(context);
            break;
        case 2: // Band Pass
            bandPassFilter.process(context);
            break;
        case 3: // Notch
            notchFilter.process(context);
            break;
    }

    // Apply delay effect
    float delayFeedback = *parameters.getRawParameterValue(AuraBloomParameters::DELAY_FEEDBACK_ID) / 100.0f;

    if (delayFeedback > 0.01f) // Only process delay if feedback is significant
    {
        for (int channel = 0; channel < buffer.getNumChannels(); ++channel)
        {
            float* channelData = buffer.getWritePointer(channel);

            for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
            {
                float input = channelData[sample];
                float delayed = delayLine.popSample(channel);

                // Mix input with delayed signal
                float output = input + delayed * delayFeedback * 0.5f; // Reduce feedback to prevent runaway

                // Push the mixed signal back into delay line
                delayLine.pushSample(channel, output);

                channelData[sample] = output;
            }
        }
    }

    // Apply reverb
    reverb.process(context);
}

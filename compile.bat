@echo off
echo Starting AuraBloom compilation...

REM Set up Visual Studio environment
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\VsDevCmd.bat"

REM Change to project directory
cd /d "d:\Augment Cod\AuraBloom_Complete_Windows_Package"

REM Clean previous build
echo Cleaning previous build...
msbuild "Builds\VisualStudio2022\AuraBloom.sln" /t:Clean /p:Configuration=Release /p:Platform=x64

REM Build only VST3 target
echo Building AuraBloom VST3...
msbuild "Builds\VisualStudio2022\AuraBloom.sln" /t:"AuraBloom - VST3" /p:Configuration=Release /p:Platform=x64 /m /v:minimal

echo Build completed. Checking results...

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Plugin location:
    echo Builds\VisualStudio2022\x64\Release\VST3\AuraBloom.vst3
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo Error code: %ERRORLEVEL%
    echo.
    echo Check build_log.txt for detailed error information.
)

pause

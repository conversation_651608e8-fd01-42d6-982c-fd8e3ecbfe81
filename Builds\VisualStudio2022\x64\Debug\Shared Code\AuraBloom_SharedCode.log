﻿  PluginProcessor.cpp
  PluginEditor.cpp
  Particle.cpp
  ParticleSystem.cpp
  Emitter.cpp
  BloomChamberComponent.cpp
  AudioBufferManager.cpp
  GrainGenerator.cpp
  ModulationSource.cpp
  LFO.cpp
  Envelope.cpp
  ModulationTarget.cpp
  ParameterModulationTarget.cpp
  ModulationMatrix.cpp
  ModulationSourceComponent.cpp
  LFOComponent.cpp
  EnvelopeComponent.cpp
  ModulationMatrixComponent.cpp
  XYControlPad.cpp
  MacroControlKnob.cpp
  MacroControlPanel.cpp
  PerformanceMonitor.cpp
  include_juce_audio_basics.cpp
  include_juce_audio_devices.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/XYControlPad.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/MacroControlKnob.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PerformanceMonitor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(29,52): warning C4458: “name”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(155,18):
      参见“ModulationTarget::name”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(72,42): warning C4458: “minValue”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(160,11):
      参见“ModulationTarget::minValue”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(83,42): warning C4458: “maxValue”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(161,11):
      参见“ModulationTarget::maxValue”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(94,40): warning C4458: “bipolar”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(162,10):
      参见“ModulationTarget::bipolar”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(59,71): warning C4458: “source”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.h(106,23):
      参见“ModulationSourceComponent::source”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(65,48): warning C4458: “active”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.h(107,10):
      参见“ModulationSourceComponent::active”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         with
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         [
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:             ValueType=int
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         ]
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         with
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         [
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:             ValueType=int
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         ]
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         with
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         [
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:             ValueType=int
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         ]
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         with
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         [
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:             ValueType=int
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         ]
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(107,56): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(112,47): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(120,86): warning C4100: “bounds”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(120,62): warning C4100: “g”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/Emitter.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(23,42): warning C4458: “particleSystem”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.h(77,21):
      参见“Emitter::particleSystem”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(49,33): warning C4458: “x”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.h(80,11):
      参见“Emitter::x”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(49,42): warning C4458: “y”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.h(81,11):
      参见“Emitter::y”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(55,30): warning C4458: “shape”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.h(82,11):
      参见“Emitter::shape”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(60,29): warning C4458: “rate”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.h(83,11):
      参见“Emitter::rate”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(65,33): warning C4458: “burstMode”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.h(84,10):
      参见“Emitter::burstMode”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(75,31): warning C4458: “spread”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Emitter.h(86,11):
      参见“Emitter::spread”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(29,52): warning C4458: “name”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(138,18):
      参见“ModulationSource::name”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(44,40): warning C4458: “bipolar”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(142,10):
      参见“ModulationSource::bipolar”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(54,39): warning C4458: “active”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(143,10):
      参见“ModulationSource::active”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(65,42): warning C4100: “timeOffset”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(77,39): warning C4458: “depth”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(140,11):
      参见“ModulationSource::depth”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(87,39): warning C4458: “phase”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
      参见“ModulationSource::phase”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{”
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(13,32): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(13,32): error C2838: “{ctor}”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(199,33): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(199,33): error C2838: “{dtor}”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(205,37): error C3254: “MacroControlKnob”: 类包含显式重写“paint”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(205,37): error C2838: “paint”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(214,37): error C3254: “MacroControlKnob”: 类包含显式重写“resized”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(214,37): error C2838: “resized”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(247,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupKnob”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(247,37): error C2838: “setupKnob”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(252,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupSlider”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(252,37): error C2838: “setupSlider”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(257,37): error C3254: “MacroControlKnob”: 类包含显式重写“createTabPanels”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(257,37): error C2838: “createTabPanels”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(262,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupEmittersPanel”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(262,37): error C2838: “setupEmittersPanel”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(267,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupParticlesPanel”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(267,37): error C2838: “setupParticlesPanel”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(272,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupModulationPanel”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(272,37): error C2838: “setupModulationPanel”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(277,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupEffectsPanel”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(277,37): error C2838: “setupEffectsPanel”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/Particle.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌
  (编译源文件“../../Source/PluginEditor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Particle.cpp(381,41): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\Particle.cpp(416,40): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(44,44): warning C4458: “activeParticles”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(62,9):
      参见“PerformanceMonitor::activeParticles”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(44,65): warning C4458: “totalParticles”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(63,9):
      参见“PerformanceMonitor::totalParticles”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(120,5): error C2181: 没有匹配 if 的非法 else 
  include_juce_audio_formats.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(135,22): error C2065: “particleMemory”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(22,15): warning C4458: “phase”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
      参见“ModulationSource::phase”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(140,35): warning C4458: “phase”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
      参见“ModulationSource::phase”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(150,60): warning C4458: “phase”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
      参见“ModulationSource::phase”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{”
  (编译源文件“../../Source/MacroControlKnob.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“../../Source/MacroControlKnob.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(15,36): error C2061: 语法错误: 标识符“MacroType”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(254,1): error C1075: “{”: 未找到匹配令牌
  include_juce_audio_plugin_client_ARA.cpp
  include_juce_audio_processors_ara.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/ParticleSystem.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/BloomChamberComponent.cpp”)
  
  include_juce_audio_processors_lv2_libs.cpp
  include_juce_audio_utils.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(324,61): warning C4100: “e”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(441,67): error C2059: 语法错误:“)”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(457,33): error C2065: “glowSize”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(457,59): error C2065: “glowSize”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(457,71): error C2065: “glowSize”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(457,81): error C2065: “glowSize”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(457,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(457,7):
      尝试匹配参数列表“()”时
  
  include_juce_core_CompilationTime.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(41,28): warning C4458: “strength”的声明隐藏了类成员
  (编译源文件“../../Source/ParticleSystem.cpp”)
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(65,11):
      参见“ForceField::strength”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(55,25): warning C4458: “active”的声明隐藏了类成员
  (编译源文件“../../Source/ParticleSystem.cpp”)
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(66,10):
      参见“ForceField::active”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(81,52): warning C4100: “deltaTime”: 未引用的形参
  (编译源文件“../../Source/ParticleSystem.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(117,29): warning C4458: “attracting”的声明隐藏了类成员
  (编译源文件“../../Source/ParticleSystem.cpp”)
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(134,10):
      参见“PointForceField::attracting”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(156,52): warning C4100: “deltaTime”: 未引用的形参
  (编译源文件“../../Source/ParticleSystem.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(203,52): warning C4100: “deltaTime”: 未引用的形参
  (编译源文件“../../Source/ParticleSystem.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(237,26): warning C4458: “radius”的声明隐藏了类成员
  (编译源文件“../../Source/ParticleSystem.cpp”)
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(254,11):
      参见“VortexForceField::radius”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(269,52): warning C4100: “deltaTime”: 未引用的形参
  (编译源文件“../../Source/ParticleSystem.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(290,25): warning C4458: “scale”的声明隐藏了类成员
  (编译源文件“../../Source/ParticleSystem.cpp”)
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(311,11):
      参见“NoiseForceField::scale”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(295,29): warning C4458: “timeScale”的声明隐藏了类成员
  (编译源文件“../../Source/ParticleSystem.cpp”)
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ForceField.h(312,11):
      参见“NoiseForceField::timeScale”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(32,58): warning C4458: “sampleRate”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.h(191,12):
      参见“ParticleSystem::sampleRate”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(32,74): warning C4458: “samplesPerBlock”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.h(192,9):
      参见“ParticleSystem::samplesPerBlock”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(92,9): warning C4189: “maxParticles”: 局部变量已初始化但不引用
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(278,39): warning C4458: “damping”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.h(174,11):
      参见“ParticleSystem::damping”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(349,47): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(425,47): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(455,49): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(485,47): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(515,47): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(574,47): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(650,31): warning C4456: “dx”的声明隐藏了上一个本地声明
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(627,19):
      参见“dx”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(651,31): warning C4456: “dy”的声明隐藏了上一个本地声明
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(628,19):
      参见“dy”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(652,31): warning C4456: “distSq”的声明隐藏了上一个本地声明
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(629,19):
      参见“distSq”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(612,53): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(668,46): warning C4100: “deltaTime”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(710,127): warning C4100: “inputBuffer”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(892,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  include_juce_data_structures.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1241,30): error C2065: “x”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1255,5): error C2181: 没有匹配 if 的非法 else 
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1259,17): error C2065: “particle”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1259,38): error C2065: “particle”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1261,17): error C2065: “particle”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1220,1): error C1075: “{”: 未找到匹配令牌
  include_juce_dsp.cpp
  include_juce_events.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符
  (编译源文件“../../Source/XYControlPad.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(35,13): error C2181: 没有匹配 if 的非法 else 
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(55,48): error C2065: “t”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(57,13): error C2181: 没有匹配 if 的非法 else 
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(60,48): error C2065: “t”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(66,9): error C2046: 非法的 case
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(77,17): error C2065: “envelopeValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(84,17): error C2065: “envelopeValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(54,34): error C2665: “juce::ColourGradient::ColourGradient”: 没有重载函数可以转换所有参数类型
      D:\JUCE\modules\juce_graphics\colour\juce_ColourGradient.h(100,5):
      可能是“juce::ColourGradient::ColourGradient(juce::Colour,juce::Point<float>,juce::Colour,juce::Point<float>,bool)”
          R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(54,34):
          “juce::ColourGradient::ColourGradient(juce::Colour,juce::Point<float>,juce::Colour,juce::Point<float>,bool)”: 无法将参数 2 从“juce::Colour”转换为“juce::Point<float>”
              R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(56,21):
              没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(54,34):
      尝试匹配参数列表“(juce::Colour, juce::Colour, ValueType, ValueType, bool)”时
          with
          [
              ValueType=float
          ]
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(86,20): error C2065: “envelopeValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(89,9): error C2047: 非法的 default
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(91,5): error C2059: 语法错误:“}”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(77,55): error C2065: “i”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(92,1): error C2059: 语法错误:“}”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(83,56): error C2065: “i”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(92,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(99,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(99,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(106,55): warning C4458: “sampleRate”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.h(156,12):
      参见“GrainGenerator::sampleRate”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(162,41): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(169,39): warning C4458: “density”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.h(157,11):
      参见“GrainGenerator::density”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(173,22): error C2601: “GrainGenerator::update”: 本地函数定义是非法的
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,19): error C3536: “screenPos”: 初始化之前无法使用
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(238,21): error C2601: “GrainGenerator::getActiveGrainCount”: 本地函数定义是非法的
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(253,21): error C2601: “GrainGenerator::getMaxGrains”: 本地函数定义是非法的
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(258,21): error C2601: “GrainGenerator::findInactiveGrain”: 本地函数定义是非法的
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,7):
      尝试匹配参数列表“()”时
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(271,22): error C2601: “GrainGenerator::processGrain”: 本地函数定义是非法的
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(379,59): error C2059: 语法错误:“)”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(171,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(171,7):
      尝试匹配参数列表“()”时
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(175,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(175,7):
      尝试匹配参数列表“()”时
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(198,9): error C2065: “utilizationRatio”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(204,61): error C2065: “densityMultiplier”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(187,54): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(187,73): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(204,31): error C2672: “juce::jlimit”: 未找到匹配的重载函数
      D:\JUCE\modules\juce_core\maths\juce_MathsFunctions.h(520,6):
      可能是“Type juce::jlimit(Type,Type,Type) noexcept”
          R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(204,31):
          “Type juce::jlimit(Type,Type,Type) noexcept”: 应输入 3 个参数，却提供了 2 个
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244: “参数”: 从“ValueType”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         with
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         [
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:             ValueType=float
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         ]
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(211,31): error C2065: “actualInterval”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244: “参数”: 从“ValueType”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         with
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         [
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:             ValueType=float
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         ]
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(220,26): error C2065: “baseDuration”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(200,5): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(232,74): error C2065: “playDirection”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(232,89): error C2065: “envelopeType”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(204,27): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(214,9): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(287,31): error C2065: “currentAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(298,13): error C2046: 非法的 case
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(300,28): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(303,39): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(304,17): error C2181: 没有匹配 if 的非法 else 
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(304,26): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(305,47): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(308,13): error C2046: 非法的 case
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(310,49): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(312,21): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(313,39): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,30): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(314,26): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,49): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(315,47): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(318,13): error C2046: 非法的 case
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(321,21): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(323,31): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,13): error C2064: 项不会计算为接受 1 个参数的函数
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,13):
      类不会将“operator()”或用户定义的转换运算符定义到指向函数的指针或指向函数的引用(它们接受适当数量的参数)
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,13):
      尝试匹配参数列表“()”时
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(326,26): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(328,39): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(335,13): error C2046: 非法的 case
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(341,21): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(343,40): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(348,26): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(350,48): error C2065: “currentNormalizedAge”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(357,13): error C2047: 非法的 default
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(359,9): error C2059: 语法错误:“}”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(362,33): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(379,59): error C2143: 语法错误: 缺少“;”(在“}”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(365,9): error C2317: 在行“365”上开始的“try”块没有 catch 处理程序
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(388,17): error C2065: “sampleValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(391,9): error C2318: 没有与该 catch 处理程序关联的 Try 块
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(397,9): error C2065: “sampleValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(397,24): error C2065: “envelopeValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(403,13): error C2065: “sampleValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(403,37): error C2065: “sampleValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(407,47): error C2065: “sample”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(409,53): error C2065: “sample”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(409,61): error C2065: “sampleValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(409,26): error C2660: “juce::AudioBuffer<float>::addSample”: 函数不接受 1 个参数
      D:\JUCE\modules\juce_audio_basics\buffers\juce_AudioSampleBuffer.h(685,10):
      参见“juce::AudioBuffer<float>::addSample”的声明
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(409,26):
      尝试匹配参数列表“(int)”时
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(412,47): error C2065: “sample”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(414,53): error C2065: “sample”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(414,61): error C2065: “sampleValue”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(414,26): error C2660: “juce::AudioBuffer<float>::addSample”: 函数不接受 1 个参数
      D:\JUCE\modules\juce_audio_basics\buffers\juce_AudioSampleBuffer.h(685,10):
      参见“juce::AudioBuffer<float>::addSample”的声明
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(414,26):
      尝试匹配参数列表“(int)”时
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(275,5): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(276,5): error C2065: “currentPosition”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(281,21): error C2039: "getParameterValue": 不是 "XYControlPad" 的成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(16,7):
      参见“XYControlPad”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(281,21): error C2270: “getParameterValue”: 非成员函数上不允许修饰符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,32): error C2065: “xAxisMappings”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,22): error C2530: “mapping”: 必须初始化引用
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,22): error C3531: “mapping”: 类型包含“auto”的符号必须具有初始值设定项
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,30): error C2143: 语法错误: 缺少“;”(在“:”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,45): error C2143: 语法错误: 缺少“;”(在“)”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,32): error C2065: “yAxisMappings”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,22): error C2530: “mapping”: 必须初始化引用
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,22): error C3531: “mapping”: 类型包含“auto”的符号必须具有初始值设定项
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,30): error C2143: 语法错误: 缺少“;”(在“:”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,45): error C2143: 语法错误: 缺少“;”(在“)”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(300,20): error C2601: “XYControlPad::updateParametersFromPosition”: 本地函数定义是非法的
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
      此行有一个“{”没有匹配项
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(307,20): error C2601: “XYControlPad::updateParameterMapping”: 本地函数定义是非法的
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
      此行有一个“{”没有匹配项
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(322,34): error C2601: “XYControlPad::screenToNormalized”: 本地函数定义是非法的
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
      此行有一个“{”没有匹配项
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(332,34): error C2601: “XYControlPad::normalizedToScreen”: 本地函数定义是非法的
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
      此行有一个“{”没有匹配项
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1): error C1075: “{”: 未找到匹配令牌
  include_juce_graphics_Harfbuzz.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,101): warning C4100: “height”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,90): warning C4100: “width”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,75): warning C4100: “rowNumber”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(213,119): warning C4100: “rowIsSelected”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(245,103): warning C4100: “isRowSelected”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(288,98): warning C4100: “e”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,104): warning C4100: “e”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,70): warning C4100: “columnId”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,55): warning C4100: “rowNumber”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(370,54): warning C4100: “lastRowSelected”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(375,57): warning C4100: “lastRowSelected”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(380,76): warning C4100: “isForwards”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(380,54): warning C4100: “newSortColumnId”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(453,21): warning C4456: “sources”的声明隐藏了上一个本地声明
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(410,17):
      参见“sources”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(454,21): warning C4456: “targets”的声明隐藏了上一个本地声明
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(422,17):
      参见“targets”的声明
  
  include_juce_gui_extra.cpp
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{”
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(15,20): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(15,20): error C2838: “{ctor}”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(26,21): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(26,21): error C2838: “{dtor}”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(30,25): error C3254: “MacroControlKnob”: 类包含显式重写“paint”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(30,25): error C2838: “paint”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(35,25): error C3254: “MacroControlKnob”: 类包含显式重写“drawBackground”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(35,25): error C2838: “drawBackground”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(65,25): error C3254: “MacroControlKnob”: 类包含显式重写“drawTitle”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(65,25): error C2838: “drawTitle”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(75,25): error C3254: “MacroControlKnob”: 类包含显式重写“resized”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(75,25): error C2838: “resized”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(101,25): error C3254: “MacroControlKnob”: 类包含显式重写“setupKnobs”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(101,25): error C2838: “setupKnobs”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(115,25): error C3254: “MacroControlKnob”: 类包含显式重写“setupPresetBrowser”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(115,25): error C2838: “setupPresetBrowser”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(158,9): error C2059: 语法错误:“)”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(160,23): error C2061: 语法错误: 标识符“savePresetButton”
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(160,5): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(160,5): warning C4183: “addAndMakeVisible”: 缺少返回类型；假定为返回“int”的成员函数
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(163,1): error C2628: “MacroControlKnob”后面接“void”是非法的(是否忘记了“;”?)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(163,6): error C2653: “MacroControlPanel”: 不是类或命名空间名称
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(165,5): error C2065: “genesisKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(166,5): error C2065: “scatterKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(167,5): error C2065: “formKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(168,5): error C2065: “chaosKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(29,49): error C2440: “return”: 无法从“int”转换为“MacroControlKnob &”
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(30,46): error C2440: “return”: 无法从“int”转换为“MacroControlKnob &”
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(31,47): error C2440: “return”: 无法从“int”转换为“MacroControlKnob &”
  (编译源文件“../../Source/MacroControlPanel.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(16,37): error C2039: "Genesis": 不是 "MacroControlKnob" 的成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(17,7):
      参见“MacroControlKnob”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(16,37): error C2065: “Genesis”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(17,37): error C2039: "Scatter": 不是 "MacroControlKnob" 的成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(17,7):
      参见“MacroControlKnob”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(17,37): error C2065: “Scatter”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(18,34): error C2039: "Form": 不是 "MacroControlKnob" 的成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(17,7):
      参见“MacroControlKnob”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(18,34): error C2065: “Form”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(19,35): error C2039: "Chaos": 不是 "MacroControlKnob" 的成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(17,7):
      参见“MacroControlKnob”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(19,35): error C2065: “Chaos”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(16,7): error C2614: “MacroControlKnob”: 非法的成员初始化:“genesisKnob”不是基或成员
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(17,7): error C2614: “MacroControlKnob”: 非法的成员初始化:“scatterKnob”不是基或成员
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(18,7): error C2614: “MacroControlKnob”: 非法的成员初始化:“formKnob”不是基或成员
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(19,7): error C2614: “MacroControlKnob”: 非法的成员初始化:“chaosKnob”不是基或成员
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(23,5): error C3861: “initializePresets”: 找不到标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(71,25): warning C4996: 'juce::Font::Font': Use the constructor that takes a FontOptions argument
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(84,5): error C2065: “presetLabel”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(86,5): error C2065: “savePresetButton”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(88,5): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(95,5): error C2065: “genesisKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(96,5): error C2065: “scatterKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(97,5): error C2065: “formKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(98,5): error C2065: “chaosKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(104,23): error C2065: “genesisKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(105,23): error C2065: “scatterKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(106,23): error C2065: “formKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(107,23): error C2065: “chaosKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(110,5): error C2065: “scatterKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(111,5): error C2065: “formKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(112,5): error C2065: “chaosKnob”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(118,5): error C2065: “presetLabel”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(119,5): error C2065: “presetLabel”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(120,5): error C2065: “presetLabel”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(121,5): error C2065: “presetLabel”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(122,23): error C2065: “presetLabel”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(125,5): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(126,5): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(127,5): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(128,5): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(129,27): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(130,13): error C3536: “selectedId”: 初始化之前无法使用
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(130,62): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(132,34): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(132,32): error C2530: “preset”: 必须初始化引用
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(133,24): error C3536: “preset”: 初始化之前无法使用
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(133,13): error C3861: “loadPreset”: 找不到标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(136,23): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(139,5): error C2065: “savePresetButton”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(140,5): error C2065: “savePresetButton”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(141,5): error C2065: “savePresetButton”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(142,5): error C2065: “savePresetButton”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(149,18): warning C4834: 放弃具有 [[nodiscard]] 属性的函数的返回值
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(154,62): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(154,21): error C3861: “savePreset”: 找不到标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(155,21): error C3861: “updatePresetComboBox”: 找不到标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(158,9): error C2143: 语法错误: 缺少“;”(在“}”的前面)
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(171,6): error C2653: “MacroControlPanel”: 不是类或命名空间名称
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(176,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(177,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(178,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(179,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(182,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(183,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(184,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(185,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(188,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(189,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(190,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(191,5): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(196,6): error C2653: “MacroControlPanel”: 不是类或命名空间名称
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(198,5): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(200,42): error C2065: “presets”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(202,9): error C2065: “presetComboBox”: 未声明的标识符
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(202,9): error C1003: 错误计数超过 100；正在停止编译
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\AudioBufferManager.cpp(23,85): warning C4458: “sampleRate”的声明隐藏了类成员
      R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\AudioBufferManager.h(162,12):
      参见“AudioBufferManager::sampleRate”的声明
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\LFOComponent.cpp(113,73): warning C4100: “bounds”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\LFOComponent.cpp(113,49): warning C4100: “g”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
  include_juce_javascript.cpp
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         with
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         [
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:             ValueType=int
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         ]
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(168,43): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(168,32): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(171,42): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(171,31): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(174,44): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(174,33): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(177,44): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(177,33): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(180,78): warning C4100: “bounds”: 未引用的形参
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(180,54): warning C4100: “g”: 未引用的形参
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-style.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{”
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob”
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(13,26): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(13,26): error C2838: “{ctor}”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(62,27): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(62,27): error C2838: “{dtor}”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(90,45): error C3254: “MacroControlKnob”: 类包含显式重写“getName”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(90,45): error C2838: “getName”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(95,31): error C3254: “MacroControlKnob”: 类包含显式重写“acceptsMidi”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(95,31): error C2838: “acceptsMidi”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(104,31): error C3254: “MacroControlKnob”: 类包含显式重写“producesMidi”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(104,31): error C2838: “producesMidi”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(113,31): error C3254: “MacroControlKnob”: 类包含显式重写“isMidiEffect”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(113,31): error C2838: “isMidiEffect”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(122,33): error C3254: “MacroControlKnob”: 类包含显式重写“getTailLengthSeconds”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(122,33): error C2838: “getTailLengthSeconds”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(127,30): error C3254: “MacroControlKnob”: 类包含显式重写“getNumPrograms”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(127,30): error C2838: “getNumPrograms”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(133,30): error C3254: “MacroControlKnob”: 类包含显式重写“getCurrentProgram”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(133,30): error C2838: “getCurrentProgram”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(138,31): error C3254: “MacroControlKnob”: 类包含显式重写“setCurrentProgram”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(138,31): error C2838: “setCurrentProgram”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(142,45): error C3254: “MacroControlKnob”: 类包含显式重写“getProgramName”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(142,45): error C2838: “getProgramName”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(147,31): error C3254: “MacroControlKnob”: 类包含显式重写“changeProgramName”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(147,31): error C2838: “changeProgramName”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(152,31): error C3254: “MacroControlKnob”: 类包含显式重写“prepareToPlay”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(152,31): error C2838: “prepareToPlay”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(188,31): error C3254: “MacroControlKnob”: 类包含显式重写“releaseResources”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(188,31): error C2838: “releaseResources”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(220,31): error C3254: “MacroControlKnob”: 类包含显式重写“processBlock”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(220,31): error C2838: “processBlock”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(375,31): error C3254: “MacroControlKnob”: 类包含显式重写“hasEditor”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(375,31): error C2838: “hasEditor”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(380,54): error C3254: “MacroControlKnob”: 类包含显式重写“createEditor”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(380,54): error C2838: “createEditor”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(399,31): error C3254: “MacroControlKnob”: 类包含显式重写“getStateInformation”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(399,31): error C2838: “getStateInformation”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(407,31): error C3254: “MacroControlKnob”: 类包含显式重写“setStateInformation”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(407,31): error C2838: “setStateInformation”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(424,31): error C3254: “MacroControlKnob”: 类包含显式重写“updateParameters”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(424,31): error C2838: “updateParameters”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(469,31): error C3254: “MacroControlKnob”: 类包含显式重写“mapParticleSystemToGrainGenerator”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(469,31): error C2838: “mapParticleSystemToGrainGenerator”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(528,31): error C3254: “MacroControlKnob”: 类包含显式重写“initializeModulationMatrix”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(528,31): error C2838: “initializeModulationMatrix”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(583,31): error C3254: “MacroControlKnob”: 类包含显式重写“initializeAudioEffects”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(583,31): error C2838: “initializeAudioEffects”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(607,31): error C3254: “MacroControlKnob”: 类包含显式重写“updateAudioEffects”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(607,31): error C2838: “updateAudioEffects”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(641,31): error C3254: “MacroControlKnob”: 类包含显式重写“processAudioEffects”，但并不从包含函数声明的接口派生
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(641,31): error C2838: “processAudioEffects”: 成员声明中的限定名称非法
R:\SoundFX\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌
  (编译源文件“../../Source/PluginProcessor.cpp”)
  
  include_juce_opengl.cpp
  include_juce_osc.cpp
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-algs.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-common.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\javascript\choc_javascript_QuickJS.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\javascript\choc_javascript.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\containers\choc_Value.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\platform\choc_Assert.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\text\choc_JSON.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\text\choc_UTF8.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\text\choc_StringUtilities.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\text\choc_FloatToString.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\math\choc_MathHelpers.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_javascript\choc\platform\choc_DisableAllWarnings.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-head-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-mvar-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-common.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-common.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-fvar-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix\sbix.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-stat-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-base-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc(814,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc(1715,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.cc(847,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-thai.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh(380,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  
D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh(1088,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
  

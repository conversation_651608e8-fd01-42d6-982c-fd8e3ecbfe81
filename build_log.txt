适用于 .NET Framework MSBuild 版本 17.13.19+0d9f5a35a
生成启动时间为 6/9/2025 5:04:58 PM。

     1>节点 1 上的项目“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom.sln”(默认目标)。
     1>ValidateSolutionConfiguration:
         正在生成解决方案配置“Release|x64”。
     1>项目“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom.sln”(1)正在节点 2 上生成“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_VST3ManifestHelper.vcxproj”(4) (默认目标)。
     4>PrepareForBuild:
         已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
       InitializeBuildStatus:
         正在创建“x64\Release\VST3 Manifest Helper\AuraBloo.AE791FC5.tlog\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
         正在对“x64\Release\VST3 Manifest Helper\AuraBloo.AE791FC5.tlog\unsuccessfulbuild”执行 Touch 任务。
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\CL.exe /c /ID:\JUCE\modules\juce_audio_processors\format_types\VST3_SDK /I..\..\JuceLibraryCode /ID:\JUCE\modules /Z7 /nologo /W4 /WX- /diagnostics:column /MP /Ox /GL /D _CRT_SECURE_NO_WARNINGS /D WIN32 /D _WINDOWS /D NDEBUG /D JUCE_PROJUCER_VERSION=0x80007 /D JUCE_MODULE_AVAILABLE_juce_audio_basics=1 /D JUCE_MODULE_AVAILABLE_juce_audio_devices=1 /D JUCE_MODULE_AVAILABLE_juce_audio_formats=1 /D JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1 /D JUCE_MODULE_AVAILABLE_juce_audio_processors=1 /D JUCE_MODULE_AVAILABLE_juce_audio_utils=1 /D JUCE_MODULE_AVAILABLE_juce_core=1 /D JUCE_MODULE_AVAILABLE_juce_data_structures=1 /D JUCE_MODULE_AVAILABLE_juce_dsp=1 /D JUCE_MODULE_AVAILABLE_juce_events=1 /D JUCE_MODULE_AVAILABLE_juce_graphics=1 /D JUCE_MODULE_AVAILABLE_juce_gui_basics=1 /D JUCE_MODULE_AVAILABLE_juce_gui_extra=1 /D JUCE_MODULE_AVAILABLE_juce_javascript=1 /D JUCE_MODULE_AVAILABLE_juce_opengl=1 /D JUCE_MODULE_AVAILABLE_juce_osc=1 /D JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1 /D JUCE_VST3_CAN_REPLACE_VST2=0 /D JUCE_PLUGINHOST_VST=0 /D JUCE_PLUGINHOST_VST3=1 /D JUCE_PLUGINHOST_AU=1 /D JUCE_PLUGINHOST_LADSPA=0 /D JUCE_PLUGINHOST_LV2=0 /D JUCE_USE_CURL=0 /D JUCE_LOAD_CURL_SYMBOLS_LAZILY=1 /D JUCE_STRICT_REFCOUNTEDPOINTER=1 /D JUCE_ENABLE_REPAINT_DEBUGGING=0 /D JUCE_USE_XRANDR=1 /D JUCE_USE_XINERAMA=1 /D JUCE_USE_XSHM=1 /D JUCE_USE_XRENDER=0 /D JUCE_USE_XCURSOR=1 /D JUCE_WEB_BROWSER=0 /D JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0 /D JucePlugin_Build_VST=0 /D JucePlugin_Build_VST3=0 /D JucePlugin_Build_AU=0 /D JucePlugin_Build_AUv3=0 /D JucePlugin_Build_AAX=0 /D JucePlugin_Build_Standalone=0 /D JucePlugin_Build_Unity=0 /D JucePlugin_Build_LV2=0 /D JucePlugin_Enable_IAA=0 /D JucePlugin_Enable_ARA=0 /D "JucePlugin_Name=\"AuraBloom\"" /D "JucePlugin_Desc=\"Granular synthesis plugin with particle effects\"" /D "JucePlugin_Manufacturer=\"YourCompany\"" /D "JucePlugin_ManufacturerWebsite=\"www.yourcompany.com\"" /D "JucePlugin_ManufacturerEmail=\"<EMAIL>\"" /D JucePlugin_ManufacturerCode=0x5972636d /D JucePlugin_PluginCode=0x41757262 /D JucePlugin_IsSynth=0 /D JucePlugin_WantsMidiInput=0 /D JucePlugin_ProducesMidiOutput=0 /D JucePlugin_IsMidiEffect=0 /D JucePlugin_EditorRequiresKeyboardFocus=0 /D JucePlugin_Version=1.0.0 /D JucePlugin_VersionCode=0x10000 /D "JucePlugin_VersionString=\"1.0.0\"" /D JucePlugin_VSTUniqueID=JucePlugin_PluginCode /D JucePlugin_VSTCategory=kPlugCategEffect /D "JucePlugin_Vst3Category=\"Fx\"" /D "JucePlugin_AUMainType='aufx'" /D JucePlugin_AUSubType=JucePlugin_PluginCode /D JucePlugin_AUExportPrefix=AuraBloomAU /D "JucePlugin_AUExportPrefixQuoted=\"AuraBloomAU\"" /D JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode /D JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom /D JucePlugin_AAXIdentifier=com.yourcompany.aurabloom /D JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode /D JucePlugin_AAXProductId=JucePlugin_PluginCode /D JucePlugin_AAXCategory=2 /D JucePlugin_AAXDisableBypass=0 /D JucePlugin_AAXDisableMultiMono=0 /D JucePlugin_IAAType=0x61757278 /D JucePlugin_IAASubType=JucePlugin_PluginCode /D "JucePlugin_IAAName=\"YourCompany: AuraBloom\"" /D JucePlugin_VSTNumMidiInputs=16 /D JucePlugin_VSTNumMidiOutputs=16 /D JucePlugin_ARAContentTypes=0 /D JucePlugin_ARATransformationFlags=0 /D "JucePlugin_ARAFactoryID=\"com.YourCompany.AuraBloom.factory\"" /D "JucePlugin_ARADocumentArchiveID=\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\"" /D "JucePlugin_ARACompatibleArchiveIDs=\"\"" /D JucePlugin_MaxNumInputChannels=2 /D JucePlugin_MaxNumOutputChannels=2 /D "JucePlugin_PreferredChannelConfigurations={1, 1}, {2, 2}" /D JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone /D JUCER_VS2022_78A503E=1 /D JUCE_APP_VERSION=1.0.0 /D JUCE_APP_VERSION_HEX=0x10000 /Gm- /EHsc /MD /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"x64\Release\VST3 Manifest Helper\\" /Fd"x64\Release\VST3 Manifest Helper\\juce_vst3_helper.pdb" /external:W4 /Gd /TP /FC /errorReport:queue D:\JUCE\modules\juce_audio_plugin_client\VST3\juce_VST3ManifestHelper.cpp
         juce_VST3ManifestHelper.cpp
     1>项目“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom.sln”(1)正在节点 3 上生成“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj”(5) (默认目标)。
     5>PrepareForBuild:
         已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
       InitializeBuildStatus:
         正在对“x64\Release\Shared Code\AuraBloo.7EC96809.tlog\unsuccessfulbuild”执行 Touch 任务。
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\CL.exe /c /ID:\JUCE\modules\juce_audio_processors\format_types\VST3_SDK /I..\..\JuceLibraryCode /ID:\JUCE\modules /Z7 /nologo /W4 /WX- /diagnostics:column /MP /Ox /GL /D _CRT_SECURE_NO_WARNINGS /D WIN32 /D _WINDOWS /D NDEBUG /D JUCE_PROJUCER_VERSION=0x80007 /D JUCE_MODULE_AVAILABLE_juce_audio_basics=1 /D JUCE_MODULE_AVAILABLE_juce_audio_devices=1 /D JUCE_MODULE_AVAILABLE_juce_audio_formats=1 /D JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1 /D JUCE_MODULE_AVAILABLE_juce_audio_processors=1 /D JUCE_MODULE_AVAILABLE_juce_audio_utils=1 /D JUCE_MODULE_AVAILABLE_juce_core=1 /D JUCE_MODULE_AVAILABLE_juce_data_structures=1 /D JUCE_MODULE_AVAILABLE_juce_dsp=1 /D JUCE_MODULE_AVAILABLE_juce_events=1 /D JUCE_MODULE_AVAILABLE_juce_graphics=1 /D JUCE_MODULE_AVAILABLE_juce_gui_basics=1 /D JUCE_MODULE_AVAILABLE_juce_gui_extra=1 /D JUCE_MODULE_AVAILABLE_juce_javascript=1 /D JUCE_MODULE_AVAILABLE_juce_opengl=1 /D JUCE_MODULE_AVAILABLE_juce_osc=1 /D JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1 /D JUCE_VST3_CAN_REPLACE_VST2=0 /D JUCE_PLUGINHOST_VST=0 /D JUCE_PLUGINHOST_VST3=1 /D JUCE_PLUGINHOST_AU=1 /D JUCE_PLUGINHOST_LADSPA=0 /D JUCE_PLUGINHOST_LV2=0 /D JUCE_USE_CURL=0 /D JUCE_LOAD_CURL_SYMBOLS_LAZILY=1 /D JUCE_STRICT_REFCOUNTEDPOINTER=1 /D JUCE_ENABLE_REPAINT_DEBUGGING=0 /D JUCE_USE_XRANDR=1 /D JUCE_USE_XINERAMA=1 /D JUCE_USE_XSHM=1 /D JUCE_USE_XRENDER=0 /D JUCE_USE_XCURSOR=1 /D JUCE_WEB_BROWSER=0 /D JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0 /D JucePlugin_Build_VST=0 /D JucePlugin_Build_VST3=1 /D JucePlugin_Build_AU=0 /D JucePlugin_Build_AUv3=0 /D JucePlugin_Build_AAX=0 /D JucePlugin_Build_Standalone=1 /D JucePlugin_Build_Unity=0 /D JucePlugin_Build_LV2=0 /D JucePlugin_Enable_IAA=0 /D JucePlugin_Enable_ARA=0 /D "JucePlugin_Name=\"AuraBloom\"" /D "JucePlugin_Desc=\"Granular synthesis plugin with particle effects\"" /D "JucePlugin_Manufacturer=\"YourCompany\"" /D "JucePlugin_ManufacturerWebsite=\"www.yourcompany.com\"" /D "JucePlugin_ManufacturerEmail=\"<EMAIL>\"" /D JucePlugin_ManufacturerCode=0x5972636d /D JucePlugin_PluginCode=0x41757262 /D JucePlugin_IsSynth=0 /D JucePlugin_WantsMidiInput=0 /D JucePlugin_ProducesMidiOutput=0 /D JucePlugin_IsMidiEffect=0 /D JucePlugin_EditorRequiresKeyboardFocus=0 /D JucePlugin_Version=1.0.0 /D JucePlugin_VersionCode=0x10000 /D "JucePlugin_VersionString=\"1.0.0\"" /D JucePlugin_VSTUniqueID=JucePlugin_PluginCode /D JucePlugin_VSTCategory=kPlugCategEffect /D "JucePlugin_Vst3Category=\"Fx\"" /D "JucePlugin_AUMainType='aufx'" /D JucePlugin_AUSubType=JucePlugin_PluginCode /D JucePlugin_AUExportPrefix=AuraBloomAU /D "JucePlugin_AUExportPrefixQuoted=\"AuraBloomAU\"" /D JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode /D JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom /D JucePlugin_AAXIdentifier=com.yourcompany.aurabloom /D JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode /D JucePlugin_AAXProductId=JucePlugin_PluginCode /D JucePlugin_AAXCategory=2 /D JucePlugin_AAXDisableBypass=0 /D JucePlugin_AAXDisableMultiMono=0 /D JucePlugin_IAAType=0x61757278 /D JucePlugin_IAASubType=JucePlugin_PluginCode /D "JucePlugin_IAAName=\"YourCompany: AuraBloom\"" /D JucePlugin_VSTNumMidiInputs=16 /D JucePlugin_VSTNumMidiOutputs=16 /D JucePlugin_ARAContentTypes=0 /D JucePlugin_ARATransformationFlags=0 /D "JucePlugin_ARAFactoryID=\"com.YourCompany.AuraBloom.factory\"" /D "JucePlugin_ARADocumentArchiveID=\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\"" /D "JucePlugin_ARACompatibleArchiveIDs=\"\"" /D JucePlugin_MaxNumInputChannels=2 /D JucePlugin_MaxNumOutputChannels=2 /D "JucePlugin_PreferredChannelConfigurations={1, 1}, {2, 2}" /D JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone /D JUCER_VS2022_78A503E=1 /D JUCE_APP_VERSION=1.0.0 /D JUCE_APP_VERSION_HEX=0x10000 /D JUCE_SHARED_CODE=1 /D _LIB /Gm- /EHsc /MD /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"x64\Release\Shared Code\\" /Fd"x64\Release\Shared Code\\AuraBloom.pdb" /external:W4 /Gd /TP /FC /errorReport:queue ..\..\Source\PluginProcessor.cpp ..\..\Source\PluginEditor.cpp ..\..\Source\Particle.cpp ..\..\Source\ParticleSystem.cpp ..\..\Source\Emitter.cpp ..\..\Source\BloomChamberComponent.cpp ..\..\Source\AudioBufferManager.cpp ..\..\Source\GrainGenerator.cpp ..\..\Source\ModulationSource.cpp ..\..\Source\LFO.cpp ..\..\Source\Envelope.cpp ..\..\Source\ModulationTarget.cpp ..\..\Source\ParameterModulationTarget.cpp ..\..\Source\ModulationMatrix.cpp ..\..\Source\ModulationSourceComponent.cpp ..\..\Source\LFOComponent.cpp ..\..\Source\EnvelopeComponent.cpp ..\..\Source\ModulationMatrixComponent.cpp ..\..\Source\XYControlPad.cpp ..\..\Source\MacroControlKnob.cpp ..\..\Source\MacroControlPanel.cpp ..\..\Source\PerformanceMonitor.cpp ..\..\JuceLibraryCode\include_juce_audio_basics.cpp ..\..\JuceLibraryCode\include_juce_audio_devices.cpp ..\..\JuceLibraryCode\include_juce_audio_formats.cpp ..\..\JuceLibraryCode\include_juce_audio_plugin_client_ARA.cpp ..\..\JuceLibraryCode\include_juce_audio_processors_ara.cpp ..\..\JuceLibraryCode\include_juce_audio_processors_lv2_libs.cpp ..\..\JuceLibraryCode\include_juce_audio_utils.cpp ..\..\JuceLibraryCode\include_juce_core_CompilationTime.cpp ..\..\JuceLibraryCode\include_juce_data_structures.cpp ..\..\JuceLibraryCode\include_juce_dsp.cpp ..\..\JuceLibraryCode\include_juce_events.cpp ..\..\JuceLibraryCode\include_juce_graphics_Harfbuzz.cpp ..\..\JuceLibraryCode\include_juce_gui_extra.cpp ..\..\JuceLibraryCode\include_juce_javascript.cpp ..\..\JuceLibraryCode\include_juce_opengl.cpp ..\..\JuceLibraryCode\include_juce_osc.cpp
         PluginProcessor.cpp
         PluginEditor.cpp
         Particle.cpp
         ParticleSystem.cpp
         Emitter.cpp
         BloomChamberComponent.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         AudioBufferManager.cpp
         GrainGenerator.cpp
         ModulationSource.cpp
         LFO.cpp
         Envelope.cpp
         ModulationTarget.cpp
         ParameterModulationTarget.cpp
         ModulationMatrix.cpp
         ModulationSourceComponent.cpp
         LFOComponent.cpp
         EnvelopeComponent.cpp
         ModulationMatrixComponent.cpp
         XYControlPad.cpp
         MacroControlKnob.cpp
         MacroControlPanel.cpp
         PerformanceMonitor.cpp
         include_juce_audio_basics.cpp
         include_juce_audio_devices.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlKnob.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PerformanceMonitor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/XYControlPad.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     4>ResourceCompile:
         D:\Windows Kits\10\bin\10.0.22621.0\x64\rc.exe /D _CRT_SECURE_NO_WARNINGS /D WIN32 /D _WINDOWS /D NDEBUG /D JUCE_PROJUCER_VERSION=0x80007 /D JUCE_MODULE_AVAILABLE_juce_audio_basics=1 /D JUCE_MODULE_AVAILABLE_juce_audio_devices=1 /D JUCE_MODULE_AVAILABLE_juce_audio_formats=1 /D JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1 /D JUCE_MODULE_AVAILABLE_juce_audio_processors=1 /D JUCE_MODULE_AVAILABLE_juce_audio_utils=1 /D JUCE_MODULE_AVAILABLE_juce_core=1 /D JUCE_MODULE_AVAILABLE_juce_data_structures=1 /D JUCE_MODULE_AVAILABLE_juce_dsp=1 /D JUCE_MODULE_AVAILABLE_juce_events=1 /D JUCE_MODULE_AVAILABLE_juce_graphics=1 /D JUCE_MODULE_AVAILABLE_juce_gui_basics=1 /D JUCE_MODULE_AVAILABLE_juce_gui_extra=1 /D JUCE_MODULE_AVAILABLE_juce_javascript=1 /D JUCE_MODULE_AVAILABLE_juce_opengl=1 /D JUCE_MODULE_AVAILABLE_juce_osc=1 /D JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1 /D JUCE_VST3_CAN_REPLACE_VST2=0 /D JUCE_PLUGINHOST_VST=0 /D JUCE_PLUGINHOST_VST3=1 /D JUCE_PLUGINHOST_AU=1 /D JUCE_PLUGINHOST_LADSPA=0 /D JUCE_PLUGINHOST_LV2=0 /D JUCE_USE_CURL=0 /D JUCE_LOAD_CURL_SYMBOLS_LAZILY=1 /D JUCE_STRICT_REFCOUNTEDPOINTER=1 /D JUCE_ENABLE_REPAINT_DEBUGGING=0 /D JUCE_USE_XRANDR=1 /D JUCE_USE_XINERAMA=1 /D JUCE_USE_XSHM=1 /D JUCE_USE_XRENDER=0 /D JUCE_USE_XCURSOR=1 /D JUCE_WEB_BROWSER=0 /D JUCE_ENABLE_LIVE_CONSTANT_EDITOR=0 /D JucePlugin_Build_VST=0 /D JucePlugin_Build_VST3=0 /D JucePlugin_Build_AU=0 /D JucePlugin_Build_AUv3=0 /D JucePlugin_Build_AAX=0 /D JucePlugin_Build_Standalone=0 /D JucePlugin_Build_Unity=0 /D JucePlugin_Build_LV2=0 /D JucePlugin_Enable_IAA=0 /D JucePlugin_Enable_ARA=0 /D "JucePlugin_Name=\\\"AuraBloom\\\"" /D "JucePlugin_Desc=\\\"Granular synthesis plugin with particle effects\\\"" /D "JucePlugin_Manufacturer=\\\"YourCompany\\\"" /D "JucePlugin_ManufacturerWebsite=\\\"www.yourcompany.com\\\"" /D "JucePlugin_ManufacturerEmail=\\\"<EMAIL>\\\"" /D JucePlugin_ManufacturerCode=0x5972636d /D JucePlugin_PluginCode=0x41757262 /D JucePlugin_IsSynth=0 /D JucePlugin_WantsMidiInput=0 /D JucePlugin_ProducesMidiOutput=0 /D JucePlugin_IsMidiEffect=0 /D JucePlugin_EditorRequiresKeyboardFocus=0 /D JucePlugin_Version=1.0.0 /D JucePlugin_VersionCode=0x10000 /D "JucePlugin_VersionString=\\\"1.0.0\\\"" /D JucePlugin_VSTUniqueID=JucePlugin_PluginCode /D JucePlugin_VSTCategory=kPlugCategEffect /D "JucePlugin_Vst3Category=\\\"Fx\\\"" /D "JucePlugin_AUMainType='aufx'" /D JucePlugin_AUSubType=JucePlugin_PluginCode /D JucePlugin_AUExportPrefix=AuraBloomAU /D "JucePlugin_AUExportPrefixQuoted=\\\"AuraBloomAU\\\"" /D JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode /D JucePlugin_CFBundleIdentifier=com.YourCompany.AuraBloom /D JucePlugin_AAXIdentifier=com.yourcompany.aurabloom /D JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode /D JucePlugin_AAXProductId=JucePlugin_PluginCode /D JucePlugin_AAXCategory=2 /D JucePlugin_AAXDisableBypass=0 /D JucePlugin_AAXDisableMultiMono=0 /D JucePlugin_IAAType=0x61757278 /D JucePlugin_IAASubType=JucePlugin_PluginCode /D "JucePlugin_IAAName=\\\"YourCompany: AuraBloom\\\"" /D JucePlugin_VSTNumMidiInputs=16 /D JucePlugin_VSTNumMidiOutputs=16 /D JucePlugin_ARAContentTypes=0 /D JucePlugin_ARATransformationFlags=0 /D "JucePlugin_ARAFactoryID=\\\"com.YourCompany.AuraBloom.factory\\\"" /D "JucePlugin_ARADocumentArchiveID=\\\"com.YourCompany.AuraBloom.aradocumentarchive.1.0.0\\\"" /D "JucePlugin_ARACompatibleArchiveIDs=\\\"\\\"" /D JucePlugin_MaxNumInputChannels=2 /D JucePlugin_MaxNumOutputChannels=2 /D "JucePlugin_PreferredChannelConfigurations={1, 1}, {2, 2}" /D JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone /D JUCER_VS2022_78A503E=1 /D JUCE_APP_VERSION=1.0.0 /D JUCE_APP_VERSION_HEX=0x10000 /l"0x0409" /ID:\JUCE\modules\juce_audio_processors\format_types\VST3_SDK /I..\..\JuceLibraryCode /ID:\JUCE\modules /nologo /fo"x64\Release\VST3 Manifest Helper\resources.res" .\resources.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.43.34808\bin\HostX64\x64\link.exe /ERRORREPORT:QUEUE /OUT:"d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\x64\Release\VST3 Manifest Helper\\juce_vst3_helper.exe" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"x64\Release\VST3 Manifest Helper\\juce_vst3_helper.pdb" /SUBSYSTEM:CONSOLE /LARGEADDRESSAWARE /OPT:REF /OPT:ICF /LTCG /LTCGOUT:"x64\Release\VST3 Manifest Helper\juce_vst3_helper.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\x64\Release\VST3 Manifest Helper\juce_vst3_helper.lib" /MACHINE:X64 "x64\Release\VST3 Manifest Helper\resources.res"
         "x64\Release\VST3 Manifest Helper\\juce_VST3ManifestHelper.obj"
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(44,44): warning C4458: “activeParticles”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(62,9):
             参见“PerformanceMonitor::activeParticles”的声明
         
       Link:
         正在生成代码
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(44,65): warning C4458: “totalParticles”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(63,9):
             参见“PerformanceMonitor::totalParticles”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(120,5): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(135,22): error C2065: “particleMemory”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         include_juce_audio_formats.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(13,26): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(13,26): error C2838: “{ctor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(62,27): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(62,27): error C2838: “{dtor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(90,45): error C3254: “MacroControlKnob”: 类包含显式重写“getName”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(90,45): error C2838: “getName”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(95,31): error C3254: “MacroControlKnob”: 类包含显式重写“acceptsMidi”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(95,31): error C2838: “acceptsMidi”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(104,31): error C3254: “MacroControlKnob”: 类包含显式重写“producesMidi”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(104,31): error C2838: “producesMidi”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(113,31): error C3254: “MacroControlKnob”: 类包含显式重写“isMidiEffect”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(113,31): error C2838: “isMidiEffect”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(122,33): error C3254: “MacroControlKnob”: 类包含显式重写“getTailLengthSeconds”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(122,33): error C2838: “getTailLengthSeconds”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(127,30): error C3254: “MacroControlKnob”: 类包含显式重写“getNumPrograms”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(127,30): error C2838: “getNumPrograms”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(133,30): error C3254: “MacroControlKnob”: 类包含显式重写“getCurrentProgram”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(133,30): error C2838: “getCurrentProgram”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(138,31): error C3254: “MacroControlKnob”: 类包含显式重写“setCurrentProgram”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(138,31): error C2838: “setCurrentProgram”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(142,45): error C3254: “MacroControlKnob”: 类包含显式重写“getProgramName”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(142,45): error C2838: “getProgramName”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(147,31): error C3254: “MacroControlKnob”: 类包含显式重写“changeProgramName”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(147,31): error C2838: “changeProgramName”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(152,31): error C3254: “MacroControlKnob”: 类包含显式重写“prepareToPlay”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(152,31): error C2838: “prepareToPlay”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(188,31): error C3254: “MacroControlKnob”: 类包含显式重写“releaseResources”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(188,31): error C2838: “releaseResources”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(220,31): error C3254: “MacroControlKnob”: 类包含显式重写“processBlock”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(220,31): error C2838: “processBlock”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(375,31): error C3254: “MacroControlKnob”: 类包含显式重写“hasEditor”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(375,31): error C2838: “hasEditor”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(380,54): error C3254: “MacroControlKnob”: 类包含显式重写“createEditor”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(380,54): error C2838: “createEditor”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(399,31): error C3254: “MacroControlKnob”: 类包含显式重写“getStateInformation”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(399,31): error C2838: “getStateInformation”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(407,31): error C3254: “MacroControlKnob”: 类包含显式重写“setStateInformation”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(407,31): error C2838: “setStateInformation”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(424,31): error C3254: “MacroControlKnob”: 类包含显式重写“updateParameters”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(424,31): error C2838: “updateParameters”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(469,31): error C3254: “MacroControlKnob”: 类包含显式重写“mapParticleSystemToGrainGenerator”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(469,31): error C2838: “mapParticleSystemToGrainGenerator”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(528,31): error C3254: “MacroControlKnob”: 类包含显式重写“initializeModulationMatrix”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(528,31): error C2838: “initializeModulationMatrix”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(583,31): error C3254: “MacroControlKnob”: 类包含显式重写“initializeAudioEffects”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(583,31): error C2838: “initializeAudioEffects”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(607,31): error C3254: “MacroControlKnob”: 类包含显式重写“updateAudioEffects”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(607,31): error C2838: “updateAudioEffects”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(641,31): error C3254: “MacroControlKnob”: 类包含显式重写“processAudioEffects”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(641,31): error C2838: “processAudioEffects”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginProcessor.cpp”)
         
         include_juce_audio_plugin_client_ARA.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(13,32): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(13,32): error C2838: “{ctor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(199,33): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(199,33): error C2838: “{dtor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(205,37): error C3254: “MacroControlKnob”: 类包含显式重写“paint”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(205,37): error C2838: “paint”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(214,37): error C3254: “MacroControlKnob”: 类包含显式重写“resized”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(214,37): error C2838: “resized”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(247,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupKnob”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(247,37): error C2838: “setupKnob”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(252,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupSlider”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(252,37): error C2838: “setupSlider”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(257,37): error C3254: “MacroControlKnob”: 类包含显式重写“createTabPanels”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(257,37): error C2838: “createTabPanels”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(262,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupEmittersPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(262,37): error C2838: “setupEmittersPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(267,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupParticlesPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(267,37): error C2838: “setupParticlesPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(272,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupModulationPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(272,37): error C2838: “setupModulationPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(277,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupEffectsPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(277,37): error C2838: “setupEffectsPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/PluginEditor.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/Emitter.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/Particle.cpp”)
         
         include_juce_audio_processors_ara.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(23,42): warning C4458: “particleSystem”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.h(77,21):
             参见“Emitter::particleSystem”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(49,33): warning C4458: “x”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.h(80,11):
             参见“Emitter::x”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(49,42): warning C4458: “y”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.h(81,11):
             参见“Emitter::y”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(55,30): warning C4458: “shape”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.h(82,11):
             参见“Emitter::shape”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(60,29): warning C4458: “rate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.h(83,11):
             参见“Emitter::rate”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(65,33): warning C4458: “burstMode”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.h(84,10):
             参见“Emitter::burstMode”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(75,31): warning C4458: “spread”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.h(86,11):
             参见“Emitter::spread”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Particle.cpp(381,41): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Particle.cpp(416,40): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(15,20): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(15,20): error C2838: “{ctor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(26,21): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(26,21): error C2838: “{dtor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(30,25): error C3254: “MacroControlKnob”: 类包含显式重写“paint”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(30,25): error C2838: “paint”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(35,25): error C3254: “MacroControlKnob”: 类包含显式重写“drawBackground”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(35,25): error C2838: “drawBackground”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(65,25): error C3254: “MacroControlKnob”: 类包含显式重写“drawTitle”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(65,25): error C2838: “drawTitle”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(75,25): error C3254: “MacroControlKnob”: 类包含显式重写“resized”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(75,25): error C2838: “resized”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(101,25): error C3254: “MacroControlKnob”: 类包含显式重写“setupKnobs”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(101,25): error C2838: “setupKnobs”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(115,25): error C3254: “MacroControlKnob”: 类包含显式重写“setupPresetBrowser”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(115,25): error C2838: “setupPresetBrowser”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(158,9): error C2187: 语法错误: 此处出现意外的“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(163,25): error C3254: “MacroControlKnob”: 类包含显式重写“setParameterChangeCallback”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(163,25): error C2838: “setParameterChangeCallback”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(171,25): error C3254: “MacroControlKnob”: 类包含显式重写“initializePresets”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(171,25): error C2838: “initializePresets”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(196,25): error C3254: “MacroControlKnob”: 类包含显式重写“updatePresetComboBox”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(196,25): error C2838: “updatePresetComboBox”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(206,25): error C3254: “MacroControlKnob”: 类包含显式重写“savePreset”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(206,25): error C2838: “savePreset”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(227,45): error C2187: 语法错误: 此处出现意外的“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(231,25): error C3254: “MacroControlKnob”: 类包含显式重写“loadPreset”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(231,25): error C2838: “loadPreset”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(246,46): error C3254: “MacroControlKnob”: 类包含显式重写“getPresetNames”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(246,46): error C2838: “getPresetNames”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlPanel.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(41,28): warning C4458: “strength”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(65,11):
             参见“ForceField::strength”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(55,25): warning C4458: “active”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(66,10):
             参见“ForceField::active”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(81,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(117,29): warning C4458: “attracting”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(134,10):
             参见“PointForceField::attracting”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(156,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(203,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(237,26): warning C4458: “radius”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(254,11):
             参见“VortexForceField::radius”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(269,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(290,25): warning C4458: “scale”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(311,11):
             参见“NoiseForceField::scale”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(295,29): warning C4458: “timeScale”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/ParticleSystem.cpp”)
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(312,11):
             参见“NoiseForceField::timeScale”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(32,58): warning C4458: “sampleRate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.h(191,12):
             参见“ParticleSystem::sampleRate”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(32,74): warning C4458: “samplesPerBlock”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.h(192,9):
             参见“ParticleSystem::samplesPerBlock”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(92,9): warning C4189: “maxParticles”: 局部变量已初始化但不引用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(203,34): warning C4101: “e”: 未引用的局部变量 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(278,39): warning C4458: “damping”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.h(174,11):
             参见“ParticleSystem::damping”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(349,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(425,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(455,49): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         include_juce_audio_processors_lv2_libs.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(485,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(515,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(574,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(650,31): warning C4456: “dx”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(627,19):
             参见“dx”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(651,31): warning C4456: “dy”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(628,19):
             参见“dy”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(652,31): warning C4456: “distSq”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(629,19):
             参见“distSq”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(612,53): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(668,46): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(710,127): warning C4100: “inputBuffer”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(892,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\AudioBufferManager.cpp(23,85): warning C4458: “sampleRate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\AudioBufferManager.h(162,12):
             参见“AudioBufferManager::sampleRate”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1241,30): error C2065: “x”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1255,5): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1259,17): error C2065: “particle”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1259,38): error C2065: “particle”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1261,17): error C2065: “particle”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1220,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(29,52): warning C4458: “name”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(155,18):
             参见“ModulationTarget::name”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(72,42): warning C4458: “minValue”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(160,11):
             参见“ModulationTarget::minValue”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(83,42): warning C4458: “maxValue”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(161,11):
             参见“ModulationTarget::maxValue”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(94,40): warning C4458: “bipolar”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.h(162,10):
             参见“ModulationTarget::bipolar”的声明
         
         include_juce_audio_utils.cpp
         include_juce_core_CompilationTime.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(22,15): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
             参见“ModulationSource::phase”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(140,35): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
             参见“ModulationSource::phase”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(150,60): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
             参见“ModulationSource::phase”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(35,13): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(55,48): error C2065: “t”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(57,13): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(60,48): error C2065: “t”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(66,9): error C2046: 非法的 case [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(77,17): error C2065: “envelopeValue”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(84,17): error C2065: “envelopeValue”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(86,20): error C2065: “envelopeValue”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(89,9): error C2047: 非法的 default [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(91,5): error C2059: 语法错误:“}” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(92,1): error C2059: 语法错误:“}” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(92,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(99,1): error C2143: 语法错误: 缺少“;”(在“{”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(99,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(106,55): warning C4458: “sampleRate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.h(156,12):
             参见“GrainGenerator::sampleRate”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(169,39): warning C4458: “density”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.h(157,11):
             参见“GrainGenerator::density”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(173,22): error C2601: “GrainGenerator::update”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(238,21): error C2601: “GrainGenerator::getActiveGrainCount”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(253,21): error C2601: “GrainGenerator::getMaxGrains”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(258,21): error C2601: “GrainGenerator::findInactiveGrain”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(271,22): error C2601: “GrainGenerator::processGrain”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(379,59): error C2187: 语法错误: 此处出现意外的“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(419,22): error C2601: “GrainGenerator::clearAllGrains”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(427,1): error C1004: 发现意外的文件尾 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         include_juce_data_structures.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(59,71): warning C4458: “source”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.h(106,23):
             参见“ModulationSourceComponent::source”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(65,48): warning C4458: “active”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.h(107,10):
             参见“ModulationSourceComponent::active”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(107,56): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(112,47): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(120,86): warning C4100: “bounds”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(120,62): warning C4100: “g”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/XYControlPad.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(54,34): error C2665: “juce::ColourGradient::ColourGradient”: 没有重载函数可以转换所有参数类型 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             D:\JUCE\modules\juce_graphics\colour\juce_ColourGradient.h(100,5):
             可能是“juce::ColourGradient::ColourGradient(juce::Colour,juce::Point<float>,juce::Colour,juce::Point<float>,bool)”
                 d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(54,34):
                 “juce::ColourGradient::ColourGradient(juce::Colour,juce::Point<float>,juce::Colour,juce::Point<float>,bool)”: 无法将参数 2 从“juce::Colour”转换为“juce::Point<float>”
                     d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(56,21):
                     没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(54,34):
             尝试匹配参数列表“(juce::Colour, juce::Colour, ValueType, ValueType, bool)”时
                 with
                 [
                     ValueType=float
                 ]
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(77,55): error C2065: “i”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(83,56): error C2065: “i”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(162,41): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,19): error C3536: “screenPos”: 初始化之前无法使用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,7):
             尝试匹配参数列表“()”时
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(171,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(171,7):
             尝试匹配参数列表“()”时
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(175,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(175,7):
             尝试匹配参数列表“()”时
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(187,54): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(187,73): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244: “参数”: 从“ValueType”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:             ValueType=float [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244: “参数”: 从“ValueType”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:             ValueType=float [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(200,5): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(204,27): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(214,9): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,30): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,49): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,13): error C2064: 项不会计算为接受 1 个参数的函数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,13):
             类不会将“operator()”或用户定义的转换运算符定义到指向函数的指针或指向函数的引用(它们接受适当数量的参数)
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,13):
             尝试匹配参数列表“()”时
         
         include_juce_dsp.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(275,5): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(276,5): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(281,21): error C2039: "getParameterValue": 不是 "XYControlPad" 的成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(16,7):
             参见“XYControlPad”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(281,21): error C2270: “getParameterValue”: 非成员函数上不允许修饰符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,32): error C2065: “xAxisMappings”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,22): error C2530: “mapping”: 必须初始化引用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,22): error C3531: “mapping”: 类型包含“auto”的符号必须具有初始值设定项 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,30): error C2143: 语法错误: 缺少“;”(在“:”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,45): error C2143: 语法错误: 缺少“;”(在“)”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,32): error C2065: “yAxisMappings”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,22): error C2530: “mapping”: 必须初始化引用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,22): error C3531: “mapping”: 类型包含“auto”的符号必须具有初始值设定项 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,30): error C2143: 语法错误: 缺少“;”(在“:”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,45): error C2143: 语法错误: 缺少“;”(在“)”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(300,20): error C2601: “XYControlPad::updateParametersFromPosition”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
             此行有一个“{”没有匹配项
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(29,52): warning C4458: “name”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(138,18):
             参见“ModulationSource::name”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(44,40): warning C4458: “bipolar”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(142,10):
             参见“ModulationSource::bipolar”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(54,39): warning C4458: “active”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(143,10):
             参见“ModulationSource::active”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(65,42): warning C4100: “timeOffset”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(77,39): warning C4458: “depth”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(140,11):
             参见“ModulationSource::depth”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(87,39): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.h(141,11):
             参见“ModulationSource::phase”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(307,20): error C2601: “XYControlPad::updateParameterMapping”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
             此行有一个“{”没有匹配项
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(322,34): error C2601: “XYControlPad::screenToNormalized”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
             此行有一个“{”没有匹配项
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(332,34): error C2601: “XYControlPad::normalizedToScreen”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1):
             此行有一个“{”没有匹配项
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         include_juce_events.cpp
         include_juce_graphics_Harfbuzz.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFOComponent.cpp(113,73): warning C4100: “bounds”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFOComponent.cpp(113,49): warning C4100: “g”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         include_juce_gui_extra.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(168,43): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(168,32): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(171,42): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(171,31): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(174,44): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(174,33): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/BloomChamberComponent.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(177,44): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(177,33): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(180,78): warning C4100: “bounds”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(180,54): warning C4100: “g”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,101): warning C4100: “height”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,90): warning C4100: “width”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,75): warning C4100: “rowNumber”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(213,119): warning C4100: “rowIsSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(245,103): warning C4100: “isRowSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(288,98): warning C4100: “e”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,104): warning C4100: “e”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,70): warning C4100: “columnId”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,55): warning C4100: “rowNumber”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(370,54): warning C4100: “lastRowSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(375,57): warning C4100: “lastRowSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(380,76): warning C4100: “isForwards”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(380,54): warning C4100: “newSortColumnId”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(453,21): warning C4456: “sources”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(410,17):
             参见“sources”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(454,21): warning C4456: “targets”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
             d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(422,17):
             参见“targets”的声明
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(324,61): warning C4100: “e”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(441,67): error C2059: 语法错误:“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         include_juce_javascript.cpp
         include_juce_opengl.cpp
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlKnob.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../Source/MacroControlKnob.cpp”)
         
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(15,36): error C2061: 语法错误: 标识符“MacroType” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
     5>d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(254,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         include_juce_osc.cpp
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-style.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
       Link:
         已完成代码的生成
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\javascript\choc_javascript_QuickJS.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\javascript\choc_javascript.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\containers\choc_Value.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\platform\choc_Assert.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-algs.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\text\choc_JSON.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\text\choc_UTF8.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\text\choc_StringUtilities.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-common.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\text\choc_FloatToString.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_javascript\choc\math\choc_MathHelpers.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
       Link:
         AuraBloom_VST3ManifestHelper.vcxproj -> d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\x64\Release\VST3 Manifest Helper\juce_vst3_helper.exe
       FinalizeBuildStatus:
         正在删除文件“x64\Release\VST3 Manifest Helper\AuraBloo.AE791FC5.tlog\unsuccessfulbuild”。
         正在对“x64\Release\VST3 Manifest Helper\AuraBloo.AE791FC5.tlog\AuraBloom_VST3ManifestHelper.lastbuildstate”执行 Touch 任务。
     4>已完成生成项目“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_VST3ManifestHelper.vcxproj”(默认目标)的操作。
     5>D:\JUCE\modules\juce_javascript\choc\platform\choc_DisableAllWarnings.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_javascript.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-head-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-mvar-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-common.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-common.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-fvar-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix\sbix.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-stat-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-base-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc(814,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc(1715,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.cc(847,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-thai.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh(380,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh(1088,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         (编译源文件“../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp”)
         
     5>已完成生成项目“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj”(默认目标)的操作 - 失败。
     1>已完成生成项目“d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom.sln”(默认目标)的操作 - 失败。

生成失败。

       “d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom.sln”(默认目标) (1) ->
       “d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj”(默认目标) (5) ->
       (ClCompile 目标) -> 
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(44,44): warning C4458: “activeParticles”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(44,65): warning C4458: “totalParticles”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(23,42): warning C4458: “particleSystem”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(49,33): warning C4458: “x”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(49,42): warning C4458: “y”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(55,30): warning C4458: “shape”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(60,29): warning C4458: “rate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(65,33): warning C4458: “burstMode”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Emitter.cpp(75,31): warning C4458: “spread”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Particle.cpp(381,41): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\Particle.cpp(416,40): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(41,28): warning C4458: “strength”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(55,25): warning C4458: “active”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(81,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(117,29): warning C4458: “attracting”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(156,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(203,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(237,26): warning C4458: “radius”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(269,52): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(290,25): warning C4458: “scale”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ForceField.h(295,29): warning C4458: “timeScale”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(32,58): warning C4458: “sampleRate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(32,74): warning C4458: “samplesPerBlock”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(92,9): warning C4189: “maxParticles”: 局部变量已初始化但不引用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(203,34): warning C4101: “e”: 未引用的局部变量 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(278,39): warning C4458: “damping”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(349,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(425,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(455,49): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(485,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(515,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(574,47): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(650,31): warning C4456: “dx”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(651,31): warning C4456: “dy”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(652,31): warning C4456: “distSq”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(612,53): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(668,46): warning C4100: “deltaTime”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(710,127): warning C4100: “inputBuffer”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(892,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\AudioBufferManager.cpp(23,85): warning C4458: “sampleRate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(29,52): warning C4458: “name”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(72,42): warning C4458: “minValue”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(83,42): warning C4458: “maxValue”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationTarget.cpp(94,40): warning C4458: “bipolar”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(22,15): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(140,35): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFO.cpp(150,60): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(106,55): warning C4458: “sampleRate”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(169,39): warning C4458: “density”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(59,71): warning C4458: “source”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(65,48): warning C4458: “active”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,59): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,55): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,31): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(88,27): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(107,56): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(112,47): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(120,86): warning C4100: “bounds”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSourceComponent.cpp(120,62): warning C4100: “g”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244: “参数”: 从“ValueType”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:             ValueType=float [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,60): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244: “参数”: 从“ValueType”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:             ValueType=float [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(188,41): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(29,52): warning C4458: “name”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(44,40): warning C4458: “bipolar”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(54,39): warning C4458: “active”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(65,42): warning C4100: “timeOffset”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(77,39): warning C4458: “depth”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationSource.cpp(87,39): warning C4458: “phase”的声明隐藏了类成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFOComponent.cpp(113,73): warning C4100: “bounds”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\LFOComponent.cpp(113,49): warning C4100: “g”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244: “参数”: 从“ValueType”转换到“float”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         with [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         [ [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:             ValueType=int [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
       d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(140,45): warning C4244:         ] [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(168,43): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(168,32): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(171,42): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(171,31): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(174,44): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(174,33): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(177,44): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(177,33): warning C4244: “参数”: 从“float”转换到“int”，可能丢失数据 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(180,78): warning C4100: “bounds”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\EnvelopeComponent.cpp(180,54): warning C4100: “g”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,101): warning C4100: “height”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,90): warning C4100: “width”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(207,75): warning C4100: “rowNumber”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(213,119): warning C4100: “rowIsSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(245,103): warning C4100: “isRowSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(288,98): warning C4100: “e”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,104): warning C4100: “e”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,70): warning C4100: “columnId”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(355,55): warning C4100: “rowNumber”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(370,54): warning C4100: “lastRowSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(375,57): warning C4100: “lastRowSelected”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(380,76): warning C4100: “isForwards”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(380,54): warning C4100: “newSortColumnId”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(453,21): warning C4456: “sources”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ModulationMatrixComponent.cpp(454,21): warning C4456: “targets”的声明隐藏了上一个本地声明 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(324,61): warning C4100: “e”: 未引用的形参 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-style.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\javascript\choc_javascript_QuickJS.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\javascript\choc_javascript.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\containers\choc_Value.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\platform\choc_Assert.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-algs.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\text\choc_JSON.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\text\choc_UTF8.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\text\choc_StringUtilities.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-common.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\text\choc_FloatToString.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\math\choc_MathHelpers.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_javascript\choc\platform\choc_DisableAllWarnings.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-head-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-mvar-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-common.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-common.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-fvar-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix\sbix.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-stat-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-base-table.hh(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc(814,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc(1715,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.cc(847,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-thai.cc(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh(380,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         D:\JUCE\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh(1088,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]


       “d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom.sln”(默认目标) (1) ->
       “d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj”(默认目标) (5) ->
       (ClCompile 目标) -> 
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(120,5): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PerformanceMonitor.cpp(135,22): error C2065: “particleMemory”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(13,26): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(13,26): error C2838: “{ctor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(62,27): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(62,27): error C2838: “{dtor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(90,45): error C3254: “MacroControlKnob”: 类包含显式重写“getName”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(90,45): error C2838: “getName”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(95,31): error C3254: “MacroControlKnob”: 类包含显式重写“acceptsMidi”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(95,31): error C2838: “acceptsMidi”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(104,31): error C3254: “MacroControlKnob”: 类包含显式重写“producesMidi”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(104,31): error C2838: “producesMidi”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(113,31): error C3254: “MacroControlKnob”: 类包含显式重写“isMidiEffect”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(113,31): error C2838: “isMidiEffect”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(122,33): error C3254: “MacroControlKnob”: 类包含显式重写“getTailLengthSeconds”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(122,33): error C2838: “getTailLengthSeconds”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(127,30): error C3254: “MacroControlKnob”: 类包含显式重写“getNumPrograms”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(127,30): error C2838: “getNumPrograms”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(133,30): error C3254: “MacroControlKnob”: 类包含显式重写“getCurrentProgram”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(133,30): error C2838: “getCurrentProgram”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(138,31): error C3254: “MacroControlKnob”: 类包含显式重写“setCurrentProgram”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(138,31): error C2838: “setCurrentProgram”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(142,45): error C3254: “MacroControlKnob”: 类包含显式重写“getProgramName”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(142,45): error C2838: “getProgramName”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(147,31): error C3254: “MacroControlKnob”: 类包含显式重写“changeProgramName”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(147,31): error C2838: “changeProgramName”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(152,31): error C3254: “MacroControlKnob”: 类包含显式重写“prepareToPlay”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(152,31): error C2838: “prepareToPlay”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(188,31): error C3254: “MacroControlKnob”: 类包含显式重写“releaseResources”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(188,31): error C2838: “releaseResources”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(220,31): error C3254: “MacroControlKnob”: 类包含显式重写“processBlock”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(220,31): error C2838: “processBlock”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(375,31): error C3254: “MacroControlKnob”: 类包含显式重写“hasEditor”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(375,31): error C2838: “hasEditor”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(380,54): error C3254: “MacroControlKnob”: 类包含显式重写“createEditor”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(380,54): error C2838: “createEditor”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(399,31): error C3254: “MacroControlKnob”: 类包含显式重写“getStateInformation”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(399,31): error C2838: “getStateInformation”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(407,31): error C3254: “MacroControlKnob”: 类包含显式重写“setStateInformation”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(407,31): error C2838: “setStateInformation”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(424,31): error C3254: “MacroControlKnob”: 类包含显式重写“updateParameters”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(424,31): error C2838: “updateParameters”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(469,31): error C3254: “MacroControlKnob”: 类包含显式重写“mapParticleSystemToGrainGenerator”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(469,31): error C2838: “mapParticleSystemToGrainGenerator”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(528,31): error C3254: “MacroControlKnob”: 类包含显式重写“initializeModulationMatrix”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(528,31): error C2838: “initializeModulationMatrix”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(583,31): error C3254: “MacroControlKnob”: 类包含显式重写“initializeAudioEffects”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(583,31): error C2838: “initializeAudioEffects”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(607,31): error C3254: “MacroControlKnob”: 类包含显式重写“updateAudioEffects”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(607,31): error C2838: “updateAudioEffects”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(641,31): error C3254: “MacroControlKnob”: 类包含显式重写“processAudioEffects”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginProcessor.cpp(641,31): error C2838: “processAudioEffects”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(13,32): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(13,32): error C2838: “{ctor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(199,33): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(199,33): error C2838: “{dtor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(205,37): error C3254: “MacroControlKnob”: 类包含显式重写“paint”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(205,37): error C2838: “paint”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(214,37): error C3254: “MacroControlKnob”: 类包含显式重写“resized”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(214,37): error C2838: “resized”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(247,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupKnob”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(247,37): error C2838: “setupKnob”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(252,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupSlider”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(252,37): error C2838: “setupSlider”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(257,37): error C3254: “MacroControlKnob”: 类包含显式重写“createTabPanels”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(257,37): error C2838: “createTabPanels”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(262,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupEmittersPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(262,37): error C2838: “setupEmittersPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(267,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupParticlesPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(267,37): error C2838: “setupParticlesPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(272,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupModulationPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(272,37): error C2838: “setupModulationPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(277,37): error C3254: “MacroControlKnob”: 类包含显式重写“setupEffectsPanel”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\PluginEditor.cpp(277,37): error C2838: “setupEffectsPanel”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(53,22): error C2079: “MacroControlKnob::MacroControlPanel::scatterKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(54,22): error C2079: “MacroControlKnob::MacroControlPanel::formKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.h(55,22): error C2079: “MacroControlKnob::MacroControlPanel::chaosKnob”使用未定义的 class“MacroControlKnob” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(15,20): error C3254: “MacroControlKnob”: 类包含显式重写“{ctor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(15,20): error C2838: “{ctor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(26,21): error C3254: “MacroControlKnob”: 类包含显式重写“{dtor}”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(26,21): error C2838: “{dtor}”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(30,25): error C3254: “MacroControlKnob”: 类包含显式重写“paint”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(30,25): error C2838: “paint”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(35,25): error C3254: “MacroControlKnob”: 类包含显式重写“drawBackground”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(35,25): error C2838: “drawBackground”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(65,25): error C3254: “MacroControlKnob”: 类包含显式重写“drawTitle”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(65,25): error C2838: “drawTitle”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(75,25): error C3254: “MacroControlKnob”: 类包含显式重写“resized”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(75,25): error C2838: “resized”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(101,25): error C3254: “MacroControlKnob”: 类包含显式重写“setupKnobs”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(101,25): error C2838: “setupKnobs”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(115,25): error C3254: “MacroControlKnob”: 类包含显式重写“setupPresetBrowser”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(115,25): error C2838: “setupPresetBrowser”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(158,9): error C2187: 语法错误: 此处出现意外的“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(163,25): error C3254: “MacroControlKnob”: 类包含显式重写“setParameterChangeCallback”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(163,25): error C2838: “setParameterChangeCallback”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(171,25): error C3254: “MacroControlKnob”: 类包含显式重写“initializePresets”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(171,25): error C2838: “initializePresets”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(196,25): error C3254: “MacroControlKnob”: 类包含显式重写“updatePresetComboBox”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(196,25): error C2838: “updatePresetComboBox”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(206,25): error C3254: “MacroControlKnob”: 类包含显式重写“savePreset”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(206,25): error C2838: “savePreset”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(227,45): error C2187: 语法错误: 此处出现意外的“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(231,25): error C3254: “MacroControlKnob”: 类包含显式重写“loadPreset”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(231,25): error C2838: “loadPreset”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(246,46): error C3254: “MacroControlKnob”: 类包含显式重写“getPresetNames”，但并不从包含函数声明的接口派生 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlPanel.cpp(246,46): error C2838: “getPresetNames”: 成员声明中的限定名称非法 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(18,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1241,30): error C2065: “x”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1255,5): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1259,17): error C2065: “particle”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1259,38): error C2065: “particle”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1261,17): error C2065: “particle”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\ParticleSystem.cpp(1220,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(35,13): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(55,48): error C2065: “t”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(57,13): error C2181: 没有匹配 if 的非法 else  [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(60,48): error C2065: “t”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(66,9): error C2046: 非法的 case [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(77,17): error C2065: “envelopeValue”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(84,17): error C2065: “envelopeValue”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(86,20): error C2065: “envelopeValue”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(89,9): error C2047: 非法的 default [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(91,5): error C2059: 语法错误:“}” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(92,1): error C2059: 语法错误:“}” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(92,1): error C2143: 语法错误: 缺少“;”(在“}”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(99,1): error C2143: 语法错误: 缺少“;”(在“{”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(99,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(173,22): error C2601: “GrainGenerator::update”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(238,21): error C2601: “GrainGenerator::getActiveGrainCount”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(253,21): error C2601: “GrainGenerator::getMaxGrains”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(258,21): error C2601: “GrainGenerator::findInactiveGrain”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(271,22): error C2601: “GrainGenerator::processGrain”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(379,59): error C2187: 语法错误: 此处出现意外的“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(419,22): error C2601: “GrainGenerator::clearAllGrains”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\GrainGenerator.cpp(427,1): error C1004: 发现意外的文件尾 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.h(51,55): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(54,34): error C2665: “juce::ColourGradient::ColourGradient”: 没有重载函数可以转换所有参数类型 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(77,55): error C2065: “i”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(83,56): error C2065: “i”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(162,41): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,19): error C3536: “screenPos”: 初始化之前无法使用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(166,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(171,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(175,7): error C2661: “juce::Graphics::fillEllipse”: 没有重载函数接受 3 个参数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(187,54): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(187,73): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(200,5): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(204,27): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(214,9): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,30): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,49): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(225,13): error C2064: 项不会计算为接受 1 个参数的函数 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(275,5): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(276,5): error C2065: “currentPosition”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(281,21): error C2039: "getParameterValue": 不是 "XYControlPad" 的成员 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(281,21): error C2270: “getParameterValue”: 非成员函数上不允许修饰符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,32): error C2065: “xAxisMappings”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,22): error C2530: “mapping”: 必须初始化引用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,22): error C3531: “mapping”: 类型包含“auto”的符号必须具有初始值设定项 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,30): error C2143: 语法错误: 缺少“;”(在“:”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(284,45): error C2143: 语法错误: 缺少“;”(在“)”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,32): error C2065: “yAxisMappings”: 未声明的标识符 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,22): error C2530: “mapping”: 必须初始化引用 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,22): error C3531: “mapping”: 类型包含“auto”的符号必须具有初始值设定项 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,30): error C2143: 语法错误: 缺少“;”(在“:”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(291,45): error C2143: 语法错误: 缺少“;”(在“)”的前面) [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(300,20): error C2601: “XYControlPad::updateParametersFromPosition”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(307,20): error C2601: “XYControlPad::updateParameterMapping”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(322,34): error C2601: “XYControlPad::screenToNormalized”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(332,34): error C2601: “XYControlPad::normalizedToScreen”: 本地函数定义是非法的 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\XYControlPad.cpp(282,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\BloomChamberComponent.cpp(441,67): error C2059: 语法错误:“)” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2059: 语法错误:“{” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.h(21,5): error C2334: “{”的前面有意外标记；跳过明显的函数体 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(15,36): error C2061: 语法错误: 标识符“MacroType” [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]
         d:\Augment Cod\AuraBloom_Complete_Windows_Package\Source\MacroControlKnob.cpp(254,1): error C1075: “{”: 未找到匹配令牌 [d:\Augment Cod\AuraBloom_Complete_Windows_Package\Builds\VisualStudio2022\AuraBloom_SharedCode.vcxproj]

    163 个警告
    193 个错误

已用时间 00:00:13.19

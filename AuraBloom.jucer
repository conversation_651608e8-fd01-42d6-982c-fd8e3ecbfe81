<?xml version="1.0" encoding="UTF-8"?>

<JUCERPROJECT id="u16SS2" name="AuraBloom" projectType="audioplug" useAppConfig="0"
              addUsingNamespaceToJuceHeader="0" jucerFormatVersion="1" pluginFormats="buildVST3,buildStandalone"
              pluginName="AuraBloom" pluginDesc="Granular synthesis plugin with particle effects"
              pluginManufacturer="YourCompany" pluginManufacturerWebsite="www.yourcompany.com"
              pluginManufacturerEmail="<EMAIL>" pluginManufacturerCode="Yrcm"
              pluginCode="Aurb" pluginChannelConfigs="{1, 1}, {2, 2}" pluginIsSynth="0"
              pluginWantsMidiIn="0" pluginProducesMidiOut="0" pluginIsMidiEffectPlugin="0"
              pluginEditorRequiresKeys="0" pluginAUExportPrefix="AuraBloomAU"
              pluginRTASCategory="" aaxIdentifier="com.yourcompany.aurabloom"
              pluginAAXCategory="2" companyName="YourCompany" companyCopyright="Copyright 2025 YourCompany"
              companyWebsite="www.yourcompany.com" companyEmail="<EMAIL>"
              displaySplashScreen="1" reportAppUsage="1" splashScreenColour="Dark"
              cppLanguageStandard="17">
  <MAINGROUP id="sZGQdC" name="AuraBloom">
    <GROUP id="{5AE4976B-08DE-464E-EFEE-7976DCB2361A}" name="Source">
      <FILE id="SgcZGN" name="PluginProcessor.cpp" compile="1" resource="0"
            file="Source/PluginProcessor.cpp"/>
      <FILE id="Az0xX8" name="PluginProcessor.h" compile="0" resource="0"
            file="Source/PluginProcessor.h"/>
      <FILE id="mqE5qO" name="PluginEditor.cpp" compile="1" resource="0"
            file="Source/PluginEditor.cpp"/>
      <FILE id="MEs9bi" name="PluginEditor.h" compile="0" resource="0" file="Source/PluginEditor.h"/>
      <FILE id="Prt001" name="PluginParameters.h" compile="0" resource="0"
            file="Source/PluginParameters.h"/>
      <FILE id="Prt002" name="Particle.h" compile="0" resource="0" file="Source/Particle.h"/>
      <FILE id="Prt003" name="Particle.cpp" compile="1" resource="0" file="Source/Particle.cpp"/>
      <FILE id="Prt004" name="ParticleSystem.h" compile="0" resource="0"
            file="Source/ParticleSystem.h"/>
      <FILE id="Prt005" name="ParticleSystem.cpp" compile="1" resource="0"
            file="Source/ParticleSystem.cpp"/>
      <FILE id="Prt006" name="Emitter.h" compile="0" resource="0" file="Source/Emitter.h"/>
      <FILE id="Prt007" name="Emitter.cpp" compile="1" resource="0" file="Source/Emitter.cpp"/>
      <FILE id="Prt008" name="BloomChamberComponent.h" compile="0" resource="0"
            file="Source/BloomChamberComponent.h"/>
      <FILE id="Prt009" name="BloomChamberComponent.cpp" compile="1" resource="0"
            file="Source/BloomChamberComponent.cpp"/>
      <FILE id="Prt010" name="AudioBufferManager.h" compile="0" resource="0"
            file="Source/AudioBufferManager.h"/>
      <FILE id="Prt011" name="AudioBufferManager.cpp" compile="1" resource="0"
            file="Source/AudioBufferManager.cpp"/>
      <FILE id="Prt012" name="GrainGenerator.h" compile="0" resource="0"
            file="Source/GrainGenerator.h"/>
      <FILE id="Prt013" name="GrainGenerator.cpp" compile="1" resource="0"
            file="Source/GrainGenerator.cpp"/>
      <FILE id="Prt014" name="ForceField.h" compile="0" resource="0" file="Source/ForceField.h"/>
      <FILE id="Prt015" name="ModulationSource.h" compile="0" resource="0"
            file="Source/ModulationSource.h"/>
      <FILE id="Prt016" name="ModulationSource.cpp" compile="1" resource="0"
            file="Source/ModulationSource.cpp"/>
      <FILE id="Prt017" name="LFO.h" compile="0" resource="0" file="Source/LFO.h"/>
      <FILE id="Prt018" name="LFO.cpp" compile="1" resource="0" file="Source/LFO.cpp"/>
      <FILE id="Prt019" name="Envelope.h" compile="0" resource="0" file="Source/Envelope.h"/>
      <FILE id="Prt020" name="Envelope.cpp" compile="1" resource="0" file="Source/Envelope.cpp"/>
      <FILE id="Prt021" name="ModulationTarget.h" compile="0" resource="0"
            file="Source/ModulationTarget.h"/>
      <FILE id="Prt022" name="ModulationTarget.cpp" compile="1" resource="0"
            file="Source/ModulationTarget.cpp"/>
      <FILE id="Prt023" name="ParameterModulationTarget.h" compile="0" resource="0"
            file="Source/ParameterModulationTarget.h"/>
      <FILE id="Prt024" name="ParameterModulationTarget.cpp" compile="1"
            resource="0" file="Source/ParameterModulationTarget.cpp"/>
      <FILE id="Prt025" name="ModulationMatrix.h" compile="0" resource="0"
            file="Source/ModulationMatrix.h"/>
      <FILE id="Prt026" name="ModulationMatrix.cpp" compile="1" resource="0"
            file="Source/ModulationMatrix.cpp"/>
      <FILE id="Prt027" name="AuraBloomLookAndFeel.h" compile="0" resource="0"
            file="Source/AuraBloomLookAndFeel.h"/>
      <FILE id="Prt028" name="ModulationSourceComponent.h" compile="0" resource="0"
            file="Source/ModulationSourceComponent.h"/>
      <FILE id="Prt029" name="ModulationSourceComponent.cpp" compile="1"
            resource="0" file="Source/ModulationSourceComponent.cpp"/>
      <FILE id="Prt030" name="LFOComponent.h" compile="0" resource="0" file="Source/LFOComponent.h"/>
      <FILE id="Prt031" name="LFOComponent.cpp" compile="1" resource="0"
            file="Source/LFOComponent.cpp"/>
      <FILE id="Prt032" name="EnvelopeComponent.h" compile="0" resource="0"
            file="Source/EnvelopeComponent.h"/>
      <FILE id="Prt033" name="EnvelopeComponent.cpp" compile="1" resource="0"
            file="Source/EnvelopeComponent.cpp"/>
      <FILE id="Prt034" name="ModulationMatrixComponent.h" compile="0" resource="0"
            file="Source/ModulationMatrixComponent.h"/>
      <FILE id="Prt035" name="ModulationMatrixComponent.cpp" compile="1"
            resource="0" file="Source/ModulationMatrixComponent.cpp"/>
      <FILE id="Prt036" name="XYControlPad.h" compile="0" resource="0"
            file="Source/XYControlPad.h"/>
      <FILE id="Prt037" name="XYControlPad.cpp" compile="1" resource="0"
            file="Source/XYControlPad.cpp"/>
      <FILE id="Prt038" name="MacroControlKnob.h" compile="0" resource="0"
            file="Source/MacroControlKnob.h"/>
      <FILE id="Prt039" name="MacroControlKnob.cpp" compile="1" resource="0"
            file="Source/MacroControlKnob.cpp"/>
      <FILE id="Prt040" name="MacroControlPanel.h" compile="0" resource="0"
            file="Source/MacroControlPanel.h"/>
      <FILE id="Prt041" name="MacroControlPanel.cpp" compile="1" resource="0"
            file="Source/MacroControlPanel.cpp"/>
      <FILE id="Prt042" name="PerformanceMonitor.h" compile="0" resource="0"
            file="Source/PerformanceMonitor.h"/>
      <FILE id="Prt043" name="PerformanceMonitor.cpp" compile="1" resource="0"
            file="Source/PerformanceMonitor.cpp"/>
    </GROUP>
  </MAINGROUP>
  <MODULES>
    <MODULE id="juce_audio_basics" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_devices" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_formats" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_plugin_client" showAllCode="1" useLocalCopy="0"
            useGlobalPath="1"/>
    <MODULE id="juce_audio_processors" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_utils" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_core" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_data_structures" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_dsp" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_events" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_graphics" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_gui_basics" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_gui_extra" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_javascript" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_opengl" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_osc" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
  </MODULES>
  <JUCEOPTIONS JUCE_STRICT_REFCOUNTEDPOINTER="1" JUCE_VST3_CAN_REPLACE_VST2="0"
               JUCE_USE_DARK_SPLASH_SCREEN="1" JUCE_PLUGINHOST_VST="0" JUCE_PLUGINHOST_VST3="1"
               JUCE_PLUGINHOST_AU="1" JUCE_PLUGINHOST_LADSPA="0" JUCE_WEB_BROWSER="0"
               JUCE_USE_CURL="0" JUCE_LOAD_CURL_SYMBOLS_LAZILY="1" JUCE_ENABLE_REPAINT_DEBUGGING="0"
               JUCE_USE_XRANDR="1" JUCE_USE_XINERAMA="1" JUCE_USE_XSHM="1" JUCE_USE_XRENDER="0"
               JUCE_USE_XCURSOR="1" JUCE_PLUGINHOST_LV2="0" JUCE_ENABLE_LIVE_CONSTANT_EDITOR="0"/>
  <EXPORTFORMATS>
    <VS2022 targetFolder="Builds/VisualStudio2022">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" targetName="AuraBloom"/>
        <CONFIGURATION isDebug="0" name="Release" targetName="AuraBloom"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_audio_basics" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_audio_devices" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_audio_formats" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_audio_plugin_client" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_audio_processors" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_audio_utils" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_core" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_data_structures" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_dsp" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_events" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_graphics" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_gui_basics" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_gui_extra" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_javascript" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_opengl" path="D:/JUCE/modules"/>
        <MODULEPATH id="juce_osc" path="D:/JUCE/modules"/>
      </MODULEPATHS>
    </VS2022>
    <XCODE_MAC targetFolder="Builds/MacOSX">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" targetName="AuraBloom" osxSDK="default"
                       osxCompatibility="10.13 SDK" osxArchitecture="arm64,x86_64" cppLanguageStandard="17"
                       cppLibType="libc++" stripLocalSymbols="1" linkTimeOptimisation="0"
                       fastMath="0" bigIcon="" smallIcon="" customXcodeFlags="GCC_GENERATE_DEBUGGING_SYMBOLS=YES;DEBUG_INFORMATION_FORMAT=dwarf-with-dsym"
                       defines="_DEBUG=1;JUCE_DEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1"
                       headerPath="" libraryPath="" customXcodeResourceFolders="" customXcodeFrameworks=""
                       extraLinkerFlags="" externalLibraries="" enablePluginBinaryCopyStep="1"
                       macOSDeploymentTarget="10.13"/>
        <CONFIGURATION isDebug="0" name="Release" targetName="AuraBloom" osxSDK="default"
                       osxCompatibility="10.13 SDK" osxArchitecture="arm64,x86_64" cppLanguageStandard="17"
                       cppLibType="libc++" stripLocalSymbols="1" linkTimeOptimisation="1"
                       fastMath="0" bigIcon="" smallIcon="" customXcodeFlags="GCC_GENERATE_DEBUGGING_SYMBOLS=NO"
                       defines="NDEBUG=1;JUCE_DISPLAY_SPLASH_SCREEN=1;JUCE_USE_DARK_SPLASH_SCREEN=1"
                       headerPath="" libraryPath="" customXcodeResourceFolders="" customXcodeFrameworks=""
                       extraLinkerFlags="" externalLibraries="" enablePluginBinaryCopyStep="1"
                       macOSDeploymentTarget="10.13"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_audio_basics" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_audio_devices" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_audio_formats" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_audio_plugin_client" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_audio_processors" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_audio_utils" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_core" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_data_structures" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_dsp" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_events" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_graphics" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_gui_basics" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_gui_extra" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_javascript" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_opengl" path="$(HOME)/JUCE/modules"/>
        <MODULEPATH id="juce_osc" path="$(HOME)/JUCE/modules"/>
      </MODULEPATHS>
    </XCODE_MAC>
  </EXPORTFORMATS>
</JUCERPROJECT>

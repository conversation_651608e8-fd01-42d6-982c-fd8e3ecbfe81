/*
  ==============================================================================

    XYControlPad.h
    Created: 21 May 2023
    Author:  AuraBloom Team

    XY控制板 - 多参数同时控制的交互式界面组件
    支持X轴和Y轴分别映射到不同的参数组合

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

class XYControlPad : public juce::Component
{
public:
    // 参数映射结构
    struct ParameterMapping
    {
        juce::String parameterId;
        float minValue;
        float maxValue;
        float currentValue;
        bool isActive;
        
        ParameterMapping(const juce::String& id, float min, float max)
            : parameterId(id), minValue(min), maxValue(max), 
              currentValue(min), isActive(true) {}
    };
    
    XYControlPad();
    ~XYControlPad() override;
    
    // Component overrides
    void paint(juce::Graphics& g) override;
    void resized() override;
    void mouseDown(const juce::MouseEvent& e) override;
    void mouseDrag(const juce::MouseEvent& e) override;
    void mouseUp(const juce::MouseEvent& e) override;
    
    // Parameter mapping management
    void addXAxisParameter(const juce::String& parameterId, float minValue, float maxValue);
    void addYAxisParameter(const juce::String& parameterId, float minValue, float maxValue);
    void removeParameter(const juce::String& parameterId);
    void clearAllMappings();
    
    // Position control
    void setXYPosition(float x, float y); // 0.0 - 1.0 range
    juce::Point<float> getXYPosition() const { return currentPosition; }
    
    // Parameter value retrieval
    float getParameterValue(const juce::String& parameterId) const;
    std::vector<ParameterMapping> getXAxisMappings() const { return xAxisMappings; }
    std::vector<ParameterMapping> getYAxisMappings() const { return yAxisMappings; }
    
    // Visual settings
    void setShowGrid(bool shouldShow) { showGrid = shouldShow; repaint(); }
    void setShowTrail(bool shouldShow) { showTrail = shouldShow; }
    void setControlPointSize(float size) { controlPointSize = juce::jlimit(5.0f, 20.0f, size); }
    
    // Callback functions
    std::function<void(const juce::String&, float)> onParameterChange;
    std::function<void(float, float)> onPositionChange;
    
private:
    // Parameter mappings
    std::vector<ParameterMapping> xAxisMappings;
    std::vector<ParameterMapping> yAxisMappings;
    
    // Current state
    juce::Point<float> currentPosition{0.5f, 0.5f}; // Normalized coordinates (0-1)
    bool isDragging = false;
    
    // Visual settings
    bool showGrid = true;
    bool showTrail = true;
    float controlPointSize = 12.0f;
    
    // 轨迹记录
    std::vector<juce::Point<float>> trailPoints;
    static constexpr int maxTrailPoints = 50;
    
    // 内部方法
    void updateParametersFromPosition();
    void updateParameterMapping(std::vector<ParameterMapping>& mappings, float normalizedValue);
    juce::Point<float> screenToNormalized(juce::Point<float> screenPoint) const;
    juce::Point<float> normalizedToScreen(juce::Point<float> normalizedPoint) const;
    
    // 绘制方法
    void drawBackground(juce::Graphics& g);
    void drawGrid(juce::Graphics& g);
    void drawParameterLabels(juce::Graphics& g);
    void drawTrail(juce::Graphics& g);
    void drawControlPoint(juce::Graphics& g);
    void drawParameterValues(juce::Graphics& g);
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(XYControlPad)
};
